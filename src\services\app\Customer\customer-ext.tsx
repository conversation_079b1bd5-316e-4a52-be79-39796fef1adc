import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/customer/customer-ext';

/**
 * Get Magento Customer groups list
 *
 * GET /api/customer/customer-ext
 */
export async function getCustomerExtListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.CustomerExt>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/**
 * create a magCustomer extension data
 *
 * PUT /api/customer/customer-ext */
export async function addCustomerExt(
  data?: API.CustomerExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.CustomerExt>>(`${urlPrefix}`, config).then(
    (res) => res.message,
  );
}

/**
 * Update magCustomer extension data.
 *
 *  PUT /api/customer/customer-ext */
export async function updateCustomerExt(
  id?: string,
  data?: API.CustomerExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.CustomerExt>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/customer/customer-ext/{id} */
export async function deleteCustomerExt(id?: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * Update magCustomer extension data.
 *
 *  PUT /api/customer/customer-ext/{id}/customer-id */
export async function updateCustomerExtByCustomerId(
  id?: number,
  data?: API.CustomerExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.CustomerExt>>(`${urlPrefix}/${id}/customer-id`, config).then(
    (res) => res.message,
  );
}
