html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.search-form {
  .ant-form-item {
    margin-bottom: 8px;
  }
  .ant-space.ant-space-horizontal {
    margin-bottom: 8px;
    margin-left: auto;
  }
  &.ant-form-inline .ant-form-item {
    margin-bottom: 8px;
  }
}

/*
General CSS classes
-------------------------------------------------------------------
*/
.cursor-pointer {
  cursor: pointer;
}

.margin-0,
.m-0 {
  margin: 0 !important;
}
.padding-0,
.p-0 {
  padding: 0 !important;
}
.py-0 {
  padding-top: 0 !important;
  padding-right: 0 !important;
}

/* .ant-table-sticky-scroll-bar {
  height: 12px !important;
} */

.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.pink {
  color: #9c5fe3;
}
.green {
  color: #026c02;
}
.red {
  color: #e00;
}
.gross-price {
  color: #9c5fe3;
}
.dark-blue {
  color: #4b81c7;
}

.c-blue {
  color: #1890ff;
}

.c-yellow {
  color: #baba09;
}

.c-orange {
  color: #f9a409;
}
.c-lightorange {
  color: #f4c877;
}

.c-darkorange {
  color: #c17c08 !important;
}

.c-lightgrey {
  color: lightgrey;
}

.c-lightgrey2 {
  color: #e3e3e3;
}

.c-grey {
  color: grey;
}

.c-green {
  color: #07c807;
}
.c-green-dark {
  color: #026c02;
}

.c-lightred {
  color: lightcoral;
}

.c-red {
  color: #ee2201;
}
.c-red8 {
  color: @red-8;
}

.c-dark-purple {
  color: #9882dc !important;
}

.bg-lightgrey {
  background: rgb(241, 240, 240) !important;
}

.bg-green {
  background: green;
}

.bg-green1 {
  background: #f6ffed !important;
}
.bg-green2 {
  background: #d9f7be !important;
}
.bg-green3 {
  background: #b7eb8f !important;
}

.bg-light-green {
  background: #f8fcf8 !important;
}

.bg-red {
  background: #f00 !important;
}
.bg-light-red {
  background: #ff8686 !important;
}

.bg-light-red1 {
  background: #f8d7d7 !important;
}

.bg-light-red2 {
  background: #f8e3e3 !important;
}
.bg-red1 {
  background: #fff1f0 !important;
}
.bg-red2 {
  background: #ffccc7 !important;
}
.bg-red3 {
  background: #ffa39e !important;
}
.bg-red4 {
  background: #ff7875 !important;
}

.bg-pink1 {
  background: #fff0f6 !important;
}
.bg-pink2 {
  background: #ffd6e7 !important;
}
.bg-pink3 {
  background: #ffadd2 !important;
}
.bg-pink4 {
  background: #ff85c0 !important;
}

.bg-light-pink2 {
  background: #f3c5cc !important;
}

.bg-light-orange {
  background: #fdc763 !important;
}
.bg-light-orange1 {
  background: #e4d3b6 !important;
}
.bg-light-orange2 {
  background: #ffeed0 !important;
}
.bg-orange1 {
  background: #fff7e6 !important;
}
.bg-orange2 {
  background: #ffe7ba !important;
}
.bg-orange3 {
  background: #ffd591 !important;
}

.bg-blue1 {
  background: #e6f4ff !important;
}
.bg-blue2 {
  background: #bae0ff !important;
}
.bg-blue3 {
  background: #91caff !important;
}
.bg-light-blue {
  background: #eef5fa !important;
}
.bg-light-blue2 {
  background: #f5f9fc !important;
}

.bg-yellow {
  background-color: yellow;
}
.bg-lightyellow {
  background-color: #f5f2d2;
}
.bg-yellow1 {
  background-color: @yellow-1;
}
.bg-yellow2 {
  background-color: @yellow-2;
}
.bg-yellow3 {
  background-color: @yellow-3;
}

.bg-gray {
  background-color: gray;
}
.bg-gray2 {
  background-color: #fafafa;
}
.bg-gray3 {
  background-color: #f5f5f5;
}
.bg-gray4 {
  background-color: #f0f0f0;
}

.btn-gray {
  color: gray;
  &:hover {
    color: #1890ff;
  }
}

.d-none {
  display: none;
}

.np {
  padding: 0 !important;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}

.bl2,
.bl-2 {
  border-left: 2px solid #000 !important;
  &.b-pink {
    border-left: 2px solid #9c5fe3 !important;
  }
  &.b-gray,
  &.b-grey {
    border-left: 2px solid #eee !important;
  }
}

.br2 {
  border-right-width: 2px !important;
  border-right-style: solid !important;
  &.b-pink {
    border-right-color: #9c5fe3 !important;
  }
  &.b-black {
    border-right-color: #000 !important;
  }
  &.b-gray,
  &.b-grey {
    border-right-color: #eee !important;
  }
}
.bb1 {
  border-bottom-width: 1px !important;
  border-bottom-style: solid !important;
  &.bb-pink {
    border-bottom-color: #9c5fe3 !important;
  }
  &.bb-black {
    border-bottom-color: #000 !important;
  }
  &.bb-gray,
  &.bb-grey {
    border-bottom-color: #eee !important;
  }
}
.bb-trans {
  border-bottom: transparent !important;
}

.h-full {
  height: auto !important;
  height: 100%;
  min-height: 100%;
}

.w-full {
  width: 100%;
}


.rotate-rl {
  transform: rotate(180deg);
  writing-mode: vertical-rl;
}

/*
Notification
-------------------------------------------------------------------
*/

/* .ant-notification-notice {
  padding: 8px 12px;
}

.ant-notification-notice-close {
  top: 12px;
} */

.ant-notification-notice-with-icon {
  max-height: 300px;
  overflow-y: auto;
}

.ant-page-header .ant-alert {
  padding: 6px 10px;
}
ul.msg {
  display: block;
  line-height: 1.2;
  list-style: outside;
  padding-inline-start: 0px;
}

ul.msg > li > ul {
  padding-left: 20px;
  list-style: circle;
  padding-inline-start: 0px;
}
// --------------------------------

/** Inline search form
-------------------------------------- */
.search-form.ant-form-inline {
  .ant-form-item {
    margin-top: 4px;
    margin-bottom: 4px;
  }

  .ant-space.ant-space-horizontal {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-left: auto;
  }
  &.ant-form-inline .ant-form-item {
    margin-top: 4px;
    margin-bottom: 4px;
  }
}

/** Table
--------------------------------------------- */
td.ant-table-cell {
  .ant-form-item .ant-form-item-control-input {
    min-height: 24px;
  }
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
