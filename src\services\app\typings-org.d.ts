declare namespace APIOrg {
  type Offer = {
    id?: string;
    offer_sid?: string;
    offer?: string;
    status?: string;
    warehouse?: string;
    supplier_id?: string;
    we?: string;
    brand?: string;
    spread_out?: string;
  } & {
    offer_template?: API.OfferTemplate;
  } & API.TimestampData;

  type OfferComment = {
    id?: string;
    offer_id?: string;
    comment?: string;
    sc_id?: string;
    customer_id?: string;
    customer_order?: string;
  } & {
    sys_config?: SysConfig;
  } & API.CreatorData & API.UpdaterData;

  type SysConfig = {
    code?: string;
    name?: string;
    value?: string;
    type?: string;
    type_sub?: string;
    order?: string;
    parent_id?: string;
    sc_customer_order?: string;
    sc_default_position?: string;
    sc_contact?: string;
    sc_customer_order_required?: string;
    option_values?: string;
    option_texts?: string;
    display_type?: string;
    value_type?: string;
    link_to_supplier?: string;
    outside_eu?: string;
    ui_border?: string;
    off_status_type?: string;
  }
}