/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/es/select';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/sys/product-category';

/** 
 * Get all product categories
 * 
 * GET /api/sys/product-category */
export async function getProductCategoryList(
  params: API.PageParams & Partial<API.ProductCategory>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.ProductCategory>> {
  return request<API.Result<API.ProductCategory>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export async function getProductCategoryACList(params: API.PageParams & Partial<API.ProductCategory>) {
  return getProductCategoryList({ ...params, pageSize: 300 }).then(res => {
    return res.data?.map(x => ({ ...x, value: x.id, label: x.name }) as RequestOptionsType)
  })
}

/** Create a product category
 * 
 *  POST /api/sys/product-category */
export async function addProductCategory(data: API.ProductCategory | FormData, options?: { [key: string]: any }): Promise<API.ProductCategory> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.ProductCategory>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** 
 * 
 * Update product category 
 * 
 * PUT /api/sys/product-category/{id} */
export async function updateProductCategory(id: number, data?: API.ProductCategory | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.ProductCategory>>(`${urlPrefix}/${id}`, config).then((res) => res.message);
}


/** delete DELETE /api/sys/product-category/{id} */
export async function deleteProductCategory(id?: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
