import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, message, Typography } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import {
  getEmailTemplateCategoryList,
  deleteEmailTemplateCategory,
} from '@/services/app/Email/email-template-category';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import CreateForm from './components/CreateForm';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.EmailTemplateCategory[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteEmailTemplateCategory({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const EmailTemplateCategoryList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<API.EmailTemplateCategory>>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.EmailTemplateCategory>();
  const [selectedRowsState, setSelectedRows] = useState<API.EmailTemplateCategory[]>([]);

  const columns: ProColumns<API.EmailTemplateCategory>[] = [
    {
      title: 'Category',
      dataIndex: 'cat1',
      sorter: true,
      width: 250,
    },
    {
      title: 'Sort',
      dataIndex: 'sort',
      className: 'c-grey',
      align: 'center',
      width: 90,
      sorter: true,
      defaultSortOrder: 'ascend',
    },
    {
      title: 'ID',
      dataIndex: 'id',
      className: 'c-grey',
      width: 90,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer extra={<Typography.Link href="/email/template">Templates</Typography.Link>}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.EmailTemplateCategory>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('cu_sf_email_template', {})}
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.setFieldsValue({});
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormText width="md" name="cat1" label="Cat1" allowClear />
        </ProForm>
      </Card>

      <ProTable<API.EmailTemplateCategory, API.PageParams>
        headerTitle={'Email Template Category List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('cu_sf_email_template_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('cu_sf_email_template', searchFormValues);
          Util.setSfValues('cu_sf_email_template_p', params);
          return getEmailTemplateCategoryList(
            { ...params, with: 'category', ...searchFormValues },
            sort,
            filter,
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'Email template category'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <BatchDeleteAction
            title="Email template category"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default EmailTemplateCategoryList;
