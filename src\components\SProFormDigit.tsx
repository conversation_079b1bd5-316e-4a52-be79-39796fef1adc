import Util from '@/util';
import { ProFormDigit } from '@ant-design/pro-form';
import type { ProFormDigitProps } from '@ant-design/pro-form/lib/components/Digit';

const SProFormDigit = (props: ProFormDigitProps & { zeroShow?: boolean }) => {
  return (
    <ProFormDigit
      {...props}
      fieldProps={{
        formatter: (value) => {
          if (value == '-') return value;
          return value !== undefined && value !== null && value !== 0 && value !== '0' && value !== ''
            ? '' + Util.numberFormat(value, props.zeroShow, props?.fieldProps?.precision, true)
            : (value as any);
        },
        parser: (x) => parseFloat(`${x}`.replace(/,/, '#').replace(/\./g, '').replace(/#/, '.')),
        decimalSeparator: ',',
        ...(props?.fieldProps || {}),
      }}
    />
  );
};

export default SProFormDigit;
