import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailAccount } from '@/services/app/Email/email-account';
import { message } from 'antd';
import Util from '@/util';
import SDatePicker from '@/components/SDatePicker';
import { getEmailServerList } from '@/services/app/Email/email-server';

const handleAdd = async (fields: API.EmailAccount) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailAccount(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!', error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailAccount>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailAccount) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Email account'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailAccount);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        name="server_id"
        label="Server"
        request={async (params) => {
          return getEmailServerList({ ...params }).then((res) =>
            res.data.map((x) => ({ value: x.id, label: x.domain })),
          );
        }}
        rules={[
          {
            required: true,
            message: 'Mail Server is required',
          },
        ]}
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Email is required',
          },
        ]}
        width="md"
        name="email"
        label="Email"
      />
      <ProFormText.Password width="md" name={['password']} label="IMAP Password" />
      <ProFormText
        width="md"
        name="sender_name"
        label="Default Sender Name"
        tooltip="Used as a default sender name in email sending. e.g. `{sender_name} <<EMAIL>>`"
      />
      <SDatePicker
        name={['settings', 'imapSince']}
        label="Since"
        width="md"
        help="Pop Emails since this date."
      />
    </ModalForm>
  );
};

export default CreateForm;
