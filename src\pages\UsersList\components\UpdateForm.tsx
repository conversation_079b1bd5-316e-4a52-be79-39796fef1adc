import React from 'react';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Status, StatusOptions, UserRole, UserRoleOptions } from '@/constants';
export type FormValueType = Partial<API.UserListItem>;

export type UpdateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  values: Partial<API.UserListItem>;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: 'User name is required',
          },
        ]}
        width="md"
        name="username"
        label="Username"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Email is required',
            type: 'email',
          },
        ]}
        width="md"
        name="email"
        label="Email"
      />
      <ProFormText
        width="md"
        name="initials"
        label="Initials"
        rules={[
          {
            max: 2,
          },
        ]}
      />
      <ProFormText.Password rules={[]} width="md" name="password" label="Password" required={false} />
      <ProFormText.Password
        rules={[
          ({ getFieldValue }) => ({
            validator(_: any, value: any, cb: any) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Confirm password does not match!'));
            },
          }),
        ]}
        width="md"
        name="confirmPassword"
        label="Confirm password"
        required={false}
      />

      <ProFormSelect
        name="role"
        label="Role"
        options={UserRoleOptions}
        initialValue={UserRole.USER}
        width="sm"
        rules={[
          {
            required: true,
            message: 'Role is required',
          },
        ]}
      />

      <ProFormSelect
        name="status"
        label="Status"
        options={StatusOptions}
        initialValue={Status.ACTIVE}
        width="sm"
        rules={[
          {
            required: true,
            message: 'Status is required',
          },
        ]}
      />
    </>
  );
};

export default UpdateForm;
