import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/offer/org-offer';

export async function getOrgOffer(id: string, params?: any) {
  return request<API.ResultObject<APIOrg.Offer>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => {
    return res.message;
  });
}