/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/es/select';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/sys/product';

/** 
 * Get all product categories
 * 
 * GET /api/sys/product */
export async function getProductList(
  params: API.PageParams & Partial<API.Product>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Product>> {
  return request<API.Result<API.Product>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export async function getProductACList(params: API.PageParams & Partial<API.Product>) {
  return getProductList({ ...params, pageSize: 300 }).then(res => {
    return res.data?.map(x => ({ ...x, value: x.id, label: x.name }) as RequestOptionsType)
  })
}

/** Create a product category
 * 
 *  POST /api/sys/product */
export async function addProduct(data: API.Product | FormData, options?: { [key: string]: any }): Promise<API.Product> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.Product>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** 
 * 
 * Update product category 
 * 
 * PUT /api/sys/product/{id} */
export async function updateProduct(id: number, data?: API.Product | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Product>>(`${urlPrefix}/${id}`, config).then((res) => res.message);
}


/** delete DELETE /api/sys/product/{id} */
export async function deleteProduct(id?: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
