/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/email-template-category';

/** 
 * Get all template categories
 * 
 * GET /api/email-template-category */
export async function getEmailTemplateCategoryList(
  params: API.PageParams & Partial<API.EmailTemplateCategory>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.EmailTemplateCategory>> {
  return request<API.Result<API.EmailTemplateCategory>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**
 * Get autocomplete list for Cat1.
 * 
 * @param params 
 * @returns 
 */
export async function getCat1ACList(params: API.PageParams) {
  return request<API.ResultObject<string[]>>(`${urlPrefix}/getCat1ACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort: { sort: 'ascend' }
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message.map(x => ({ value: x, label: x })));
}

export async function getEmailTemplateCatACList(params: API.PageParams) {
  return request<API.ResultObject<API.EmailTemplateCategory[]>>(`${urlPrefix}/getCat1ACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort: { sort: 'ascend', cat1: 'ascend' }
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message.map(x => ({ value: x.id, label: x.cat1 })));
}

/** 
 * Create Email template category
 * 
 * POST /api/email-template-category */
export async function addEmailTemplateCategory(data: API.EmailTemplateCategory, options?: { [key: string]: any }) {
  return request<API.EmailTemplateCategory>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** 
 * Update Email template category
 * 
 * PUT /api/email-template-category */
export async function updateEmailTemplateCategory(data: Partial<API.EmailTemplateCategory>, options?: { [key: string]: any }) {
  return request<API.EmailTemplateCategory>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}



/** DELETE /api/email-template-category/{id} */
export async function deleteEmailTemplateCategory(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
