import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import {
  getEmailServerList,
  deleteEmailServer,
  updateEmailServer,
} from '@/services/app/Email/email-server';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import <PERSON>ooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import EditableCell from '@/components/EditableCell';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.EmailServer[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEmailServer({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const EmailServerList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.EmailServer>();
  const [selectedRowsState, setSelectedRows] = useState<API.EmailServer[]>([]);

  const columns: ProColumns<API.EmailServer>[] = [
    {
      title: 'Domin',
      dataIndex: 'domain',
      sorter: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              handleUpdateModalVisible(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'IMAP Host',
      dataIndex: 'imap_host',
      sorter: true,
    },
    {
      title: 'Is SSL?',
      dataIndex: 'imap_ssl',
      sorter: true,
      hideInSearch: true,
      render: (dom, record) => (
        <EditableCell
          dataType="switch"
          defaultValue={record.imap_ssl}
          isDefaultEditing
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            return updateEmailServer({ id: record.id, imap_ssl: value }).then((res) =>
              actionRef.current?.reload(),
            );
          }}
        />
      ),
    },
    {
      title: 'IMAP Port',
      dataIndex: 'imap_port',
      sorter: true,
      hideInSearch: true,
      onCell: (record, index) => {
        return {
          className: record.imap_ssl ? 'c-lightgrey' : '',
        };
      },
    },
    {
      title: 'IMAP Port (SSL)',
      dataIndex: 'imap_port_ssl',
      sorter: true,
      hideInSearch: true,
      onCell: (record, index) => {
        return {
          className: !record.imap_ssl ? 'c-lightgrey' : '',
        };
      },
    },
    {
      title: 'SMTP Host',
      dataIndex: 'smtp_host',
      sorter: true,
    },
    {
      title: 'SMTP Port',
      dataIndex: 'smtp_port',
      sorter: true,
      hideInSearch: true,
      /* onCell: (record, index) => {
        return {
          className: record.imap_ssl ? 'c-lightgrey' : '',
        };
      }, */
    },
    {
      title: 'Is OAuth?',
      dataIndex: 'is_oauth',
      sorter: true,
      hideInSearch: true,
      render: (dom, record) => (
        <EditableCell
          dataType="switch"
          defaultValue={record.is_oauth}
          isDefaultEditing
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            return updateEmailServer({ id: record.id, is_oauth: value }).then((res) =>
              actionRef.current?.reload(),
            );
          }}
        />
      ),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      colSize: 1,
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.EmailServer, API.PageParams>
        headerTitle={'Email Server list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getEmailServerList}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'Email server'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <BatchDeleteAction
            title="Email server"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      {/* {selectedRowsState?.length > 0 && (
        <SFooterToolbar
          title="supplier"
          selectedRowsState={selectedRowsState}
          setSelectedRows={setSelectedRows}
          actionRef={actionRef}
          handleRemove={handleRemove}
        />
      )} */}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default EmailServerList;
