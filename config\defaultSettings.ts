import type { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // Dawn Blue
  colorPrimary: '#1890ff',
  layout: 'top',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: 'WHC Supplier',
  pwa: true,
  iconfontUrl: '',
  token: {
    // See ts declaration, demo in the document, modify the style through token
    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F

    /* header: {
      colorBgHeader: '#001529',
      colorHeaderTitle: '#ffffff',
      colorTextMenu: '#ffffff',
      colorTextMenuSecondary: 'red',
      // colorTextMenuActive: '#ffffff',
      colorTextMenuSelected: '#ffffff', // used
      colorTextRightActionsItem: '#ffffff',
    }, */

    sider: {

    },
    pageContainer: {
      paddingBlockPageContainerContent: 8,
      paddingInlinePageContainerContent: 16
    },
  },
};

export default Settings;
