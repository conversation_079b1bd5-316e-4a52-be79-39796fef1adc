import React, { useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON>ton, Card, message, Space, Tag } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import Util from '@/util';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProForm } from '@ant-design/pro-form';
import { ReloadOutlined } from '@ant-design/icons';
import { dsEmailTrackingData, getSysLogList } from '@/services/app/Sys/sys-log';
import { SysLogCategoryOptions } from '@/constants';

const SysLogList: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.SysLog>[] = [
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => (
        <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      width: 130,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      copyable: true,
      width: 350,
    },
    {
      title: 'Ref1',
      dataIndex: ['ref1'],
      copyable: true,
      width: 200,
    },
    {
      title: 'Ref2',
      dataIndex: ['ref2'],
      copyable: true,
      width: 200,
    },

    {
      title: 'Date',
      dataIndex: 'created_on',
      width: 110,
      render: (dom, record) =>
        record.created_on ? (
          <div title={Util.dtToDMYHHMM(record.created_on)}>
            {dayjs(record.created_on).fromNow()}
          </div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 120,
    },
  ];

  const loadData = () => {
    actionRef.current?.reload();
  };

  return (
    <PageContainer
      extra={
        <Space size={24}>
          <Button
            type="primary"
            ghost
            className="btn-green"
            onClick={() => {
              const hide = message.loading('Down Syncing email tracking info...', 0);
              dsEmailTrackingData()
                .then(() => {
                  message.success('Synced successfully!');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Sync Email Tracking Data
          </Button>
        </Space>
      }
    >
      <Card>
        <ProForm
          layout="inline"
          formRef={formRef}
          submitter={false}
          initialValues={Util.getSfValues('sf_log', {}, {})}
        >
          <ProFormSelect
            name="category"
            placeholder="Select category"
            label="Category"
            options={SysLogCategoryOptions}
            width={200}
            fieldProps={{ onChange: (e: any) => loadData() }}
          />
          <ProFormSelect
            name="statuses"
            placeholder="Select status"
            label="Status"
            mode="multiple"
            options={[
              { value: '', label: 'All' },
              { value: 'success', label: 'Success' },
              { value: 'started', label: 'Started' },
              { value: 'error', label: 'Error' },
              { value: 'processing', label: 'Processing' },
            ]}
            width={150}
          />
          <ProFormText name="nameLike" width={150} label="Name" />
          <ProFormText name="note" width={150} label="Note" />
          <Button type="primary" icon={<ReloadOutlined />} onClick={() => loadData()}>
            Search
          </Button>
        </ProForm>
      </Card>

      <ProTable<API.SysLog, API.PageParams>
        rowKey="id"
        size="small"
        headerTitle="System Log"
        revalidateOnFocus={false}
        search={false}
        params={{ with: 'user' }}
        scroll={{ x: 800 }}
        actionRef={actionRef}
        request={async (params, sort, filter) => {
          const sfValues = formRef.current?.getFieldsValue();
          Util.setSfValues('sf_log', sfValues);
          return getSysLogList(
            { ...params, ...sfValues, with: 'itemEan,itemEan.eanTextDe' },
            Object.keys(sort)?.length < 1 ? { id: 'descend' } : sort,
            filter,
          );
        }}
        columns={columns}
        pagination={{ showSizeChanger: true, defaultPageSize: 20 }}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />
    </PageContainer>
  );
};

export default SysLogList;
