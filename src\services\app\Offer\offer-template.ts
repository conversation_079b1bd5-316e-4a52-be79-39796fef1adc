import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/offer/template';

/**
 * Get OfferTemplates list
 *
 * GET /api/offer/template
 */
export async function getOfferTemplateListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.OfferTemplate>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getOfferTemplate(id?: number, params?: API.OfferTemplate & API.PageParams) {
  return getOfferTemplateListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}


export async function getOfferTemplateByOfferNo(offerNo?: number | string, params?: API.OfferTemplate & API.PageParams) {
  return getOfferTemplateListByPage({ offer_no: offerNo, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Create supplier.
 *
 *  POST /api/offer/template */
export async function addOfferTemplate(
  data?: API.OfferTemplate | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferTemplate>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update OfferTemplate data.
 *
 *  PUT /api/offer/template/{id}/update */
export async function updateOfferTemplate(
  id?: number,
  data?: API.OfferTemplate | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferTemplate>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}



/**
 * Update or Create OfferTemplate list.
 *
 *  POST /api/offer/template/bulk */
export async function updateOfferTemplateBulk(
  data?: API.OfferTemplate[] | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferTemplate>>(`${urlPrefix}/bulk`, config).then(
    (res) => res.message,
  );
}



/**
 * Get AC List dropdown list.
 *
 * GET /api/offer/template/getOfferTemplateACList
 */
export async function getOfferTemplateACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getOfferTemplateACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 500,
      page: params.current,
      sort: { id: 'descend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/offer/template/{id} */
export async function deleteOfferTemplate(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
