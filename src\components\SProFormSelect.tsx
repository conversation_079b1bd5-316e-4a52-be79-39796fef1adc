import Util, { sn } from '@/util';
import { ProFormSelect } from '@ant-design/pro-form';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';

const SProFormSelect = (
  props: ProFormSelectProps & {
    isNumericValue?: boolean;
    onTabKeyCallback?: (value?: any) => void;
  },
) => {
  return (
    <ProFormSelect
      {...props}
      fieldProps={{
        ...props.fieldProps,
        showSearch: true,
        onInputKeyDown: (e: any) => {
          if (props.mode == 'multiple') return;
          if (Util.isTabPressed(e)) {
            const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
            if (dropdownWrapper?.children.length == 1) {
              const id = e.target.getAttribute('aria-activedescendant');
              let value: any = document.getElementById(id)?.innerText;
              if (props.isNumericValue) {
                value = sn(value);
              }

              props.onTabKeyCallback?.(value);
            }
          }
        },
      }}
    />
  );
};

export default SProFormSelect;
