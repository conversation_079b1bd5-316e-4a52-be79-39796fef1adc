/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { sLeftPad } from '@/util';

const urlPrefix = '/api/email-template';

/** rule GET /api/email-template */
export async function getEmailTemplateList(
  params: API.PageParams & Partial<API.EmailTemplate>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.EmailTemplate>> {
  return request<API.Result<API.EmailTemplate>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getEmailTemplate(id: number): Promise<API.EmailTemplate> {
  return getEmailTemplateList({ id: id, pageSize: 1 }).then(res => res.data?.[0]);
}


/**
 * Get autocomplete list for email templates.
 * 
 * @param params 
 * @returns 
 */
export async function getEmailTemplateACList(params: API.PageParams) {
  return request<API.ResultObject<API.EmailTemplate[]>>(`${urlPrefix}/getACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      page: params.current,
      sort: { cat1_mix: 'ascend' }
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message.map(x => ({ ...x, value: x.id, label: `${x.category?.cat1} | ${x.title}${sLeftPad(x.sort, 3, '0')}` })));
  // }).then((res) => res.message.map(x => ({ ...x, value: x.id, label: `${x.title}` })));
}

/** put PUT /api/email-template */
export async function updateEmailTemplate(data: Partial<API.EmailTemplate>, options?: { [key: string]: any }) {
  return request<API.EmailTemplate>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email-template */
export async function addEmailTemplate(data: API.EmailTemplate, options?: { [key: string]: any }) {
  return request<API.EmailTemplate>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/email-template/{id} */
export async function deleteEmailTemplate(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
