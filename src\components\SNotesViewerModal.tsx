import React from 'react';
import { Modal } from 'antd';
import { Dispatch, SetStateAction } from 'react';
import HtmlEditor from './HtmlEditor';

export type SNotesViewerModalProps = {
  id: string;
  title?: string;
  content?: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  width?: number | string;
};

const SNotesViewerModal: React.FC<SNotesViewerModalProps> = (props) => {
  const { id, title, content, modalVisible, handleModalVisible, width = 1600 } = props;

  return (
    <Modal
      title={title || 'Notes'}
      width={width}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      footer={false}
    >
      {content && (
        <HtmlEditor
          id={id ?? 'notes-viewer-editor'}
          value={content}
          height={600}
          hideMenuBar
          toolbarMode={2}
          onChange={() => {}}
        />
      )}
    </Modal>
  );
};

export default SNotesViewerModal;
