import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEmail } from '@/services/app/Email/email';
import Util from '@/util';
import { getDictList } from '@/services/app/Sys/sys-dict';
import { DictType } from '@/constants';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEmail(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Email>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Email>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Email Data'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      {/* <ProFormText width="sm" name="order_id" label="Order ID" />
      <ProFormText width="sm" name="tracking_no" label="Tracking No" />
      <ProFormSelect
        name="ext_order"
        label="Ext Order"
        placeholder="Ext Order"
        showSearch
        width={150}
        request={async (params) => {
          return getDictList({ params, type: DictType.ExtOrder, pageSize: 500 }).then((res) =>
            res.data.map((x) => ({ value: x.code, label: x.value })),
          );
        }}
      />
      <ProFormText width="sm" name="ext_order_id" label="Ext Order ID" /> */}
      <ProFormSelect
        name="status"
        label="Status"
        placeholder="Status"
        showSearch
        width={150}
        request={async (params) => {
          return getDictList({ params, type: DictType.EmailStatus, pageSize: 500 }).then((res) =>
            res.data.map((x) => ({ value: x.code, label: x.value })),
          );
        }}
      />
    </ModalForm>
  );
};

export default UpdateForm;
