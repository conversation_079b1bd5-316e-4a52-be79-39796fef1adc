import { VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';

type EmailBoxTypeIconProps = {
  box?: string;
};

const EmailBoxTypeIcon: React.FC<EmailBoxTypeIconProps> = ({ box, ...rest }) => {
  let obj = null;
  if (box == 'INBOX') {
    obj = <VerticalAlignBottomOutlined style={{ color: '#52c41a' }} />;
  } else {
    obj = <VerticalAlignTopOutlined />;
  }
  return obj;
};

export default EmailBoxTypeIcon;
