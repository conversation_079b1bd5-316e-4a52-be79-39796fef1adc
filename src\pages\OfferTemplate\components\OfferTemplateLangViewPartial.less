.viewContainer {
  padding: 16px 0;
}

.langCard {
  margin-bottom: 16px;
  background-color: #fafafa;

  :global(.ant-card-head) {
    background-color: #f0f0f0;
  }
}

.section {
  margin-bottom: 24px;

  h5 {
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
}

.contentBox {
  padding: 12px;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  min-height: 60px;
  overflow: auto;

  p {
    margin-bottom: 8px;
  }
}

.filesContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
  min-height: 80px;
  padding: 12px;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.fileCard {
  width: 200px;
  margin-bottom: 8px;
}

.fileItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fileIcon {
  font-size: 18px;
  color: #1890ff;
}
