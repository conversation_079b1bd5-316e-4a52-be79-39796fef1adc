import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/supplier/supplier-ext';

/**
 * Get Magento Supplier groups list
 *
 * GET /api/supplier/supplier-ext
 */
export async function getSupplierExtListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.SupplierExt>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/**
 * create a magSupplier extension data
 *
 * PUT /api/supplier/supplier-ext */
export async function addSupplierExt(
  data?: API.SupplierExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SupplierExt>>(`${urlPrefix}`, config).then(
    (res) => res.message,
  );
}

/**
 * Update magSupplier extension data.
 *
 *  PUT /api/supplier/supplier-ext */
export async function updateSupplierExt(
  id?: string,
  data?: API.SupplierExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SupplierExt>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/supplier/supplier-ext/{id} */
export async function deleteSupplierExt(id?: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * Update magSupplier extension data.
 *
 *  PUT /api/supplier/supplier-ext/{id}/supplier-id */
export async function updateSupplierExtBySupplierId(
  id?: number,
  data?: API.SupplierExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SupplierExt>>(`${urlPrefix}/${id}/supplier-id`, config).then(
    (res) => res.message,
  );
}
