import { getEmailTemplate, getEmailTemplateACList } from '@/services/app/Email/email-template';
import Util, { sn } from '@/util';
import { ProFormSelect } from '@ant-design/pro-components';
import { Space } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';

export default () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [templateId, setTemplateId] = useState<any>();
  const [template, setTemplate] = useState<API.EmailTemplate>();

  const emailTemplateSelector = useMemo(() => {
    return (
      <Space>
        <ProFormSelect
          name="template_id"
          label="Template"
          request={async (params) => {
            setLoading(true);
            return getEmailTemplateACList({ ...params })
              .then((res) => {
                return res;
              })
              .catch((err) => {
                Util.error(err);
                return [];
              })
              .finally(() => {
                setLoading(false);
              });
          }}
          fieldProps={{ value: templateId, popupMatchSelectWidth: false }}
          onChange={(value, option) => {
            setTemplateId(value);
          }}
          width={200}
          allowClear
          showSearch
        />
      </Space>
    );
  }, [templateId]);

  const loadEmailTemplate = useCallback(() => {
    if (templateId) {
      setLoading(true);
      return getEmailTemplate(sn(templateId))
        .then((res) => setTemplate(res))
        .catch(Util.error)
        .finally(() => setLoading(false));
    } else {
      setTemplate(undefined);
    }
  }, [templateId]);

  useEffect(() => {
    loadEmailTemplate();
  }, [loadEmailTemplate]);

  return {
    emailTemplateSelector,
    loadEmailTemplate,
    template,
    setTemplateId,
    loading,
  };
};
