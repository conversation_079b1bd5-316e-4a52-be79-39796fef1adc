import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/supplier/supplier-call';

/**
 * Get Magento Supplier groups list
 *
 * GET /api/supplier/supplier-call
 */
export async function getSupplierCallListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.SupplierCall>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/**
 * create a call log
 *
 * PUT /api/supplier/supplier-call */
export async function addSupplierCall(
  data?: API.SupplierCall | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SupplierCall>>(`${urlPrefix}`, config).then(
    (res) => res.message,
  );
}

/**
 * Update call log.
 *
 *  PUT /api/supplier/supplier-call/{di} */
export async function updateSupplierCall(
  id?: number,
  data?: API.SupplierCall | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SupplierCall>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/supplier/supplier-call/{id} */
export async function deleteSupplierCall(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * Get Offer Comments List from WHC_Org
 *
 * GET /api/supplier/supplier-call/getOrgOfferCommentAll
 */
export async function getOrgOfferCommentAll(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<APIOrg.OfferComment>>(`${urlPrefix}/getOrgOfferCommentAll`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}
