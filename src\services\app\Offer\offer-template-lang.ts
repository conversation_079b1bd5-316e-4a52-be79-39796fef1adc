import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/offer/template-lang';

/**
 * Get OfferTemplateLangs list
 *
 * GET /api/offer/template-lang
 */
export async function getOfferTemplateLangListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.OfferTemplateLang>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getOfferTemplateLang(id?: number, params?: API.OfferTemplateLang & API.PageParams) {
  return getOfferTemplateLangListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}


/**
 * Create OfferTemplateLang.
 *
 *  POST /api/offer/template-lang */
export async function addOfferTemplateLang(
  data?: API.OfferTemplateLang | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferTemplateLang>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update OfferTemplateLang data.
 *
 *  PUT /api/offer/template-lang/{id}/update */
export async function updateOfferTemplateLang(
  id?: number,
  data?: API.OfferTemplateLang | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferTemplateLang>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}


/** delete DELETE /api/offer/template-lang/{id} */
export async function deleteOfferTemplateLang(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
