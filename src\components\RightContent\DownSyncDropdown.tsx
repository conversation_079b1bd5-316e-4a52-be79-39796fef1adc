import React, { useCallback } from 'react';
import { SyncOutlined } from '@ant-design/icons';
import { Menu } from 'antd';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import type { MenuInfo } from 'rc-menu/lib/interface';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const DownSyncDropdown: React.FC<GlobalHeaderRightProps> = ({}) => {
  const onMenuClick = useCallback((event: MenuInfo) => {
    const { key } = event;

    return;
  }, []);

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick} items={[]} />
  );

  return (
    <HeaderDropdown menu={{ items: [] }}>
      <span className={`${styles.action} ${styles.account}`}>
        <SyncOutlined /> &nbsp;
        <span className={`${styles.name} anticon`}>Down Sync</span>
      </span>
    </HeaderDropdown>
  );
};

export default DownSyncDropdown;
