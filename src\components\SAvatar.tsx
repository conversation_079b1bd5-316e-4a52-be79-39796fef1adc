import type { AvatarProps } from 'antd';
import { Avatar } from 'antd';
import type { RefAttributes } from 'react';

type SAvatarProps = {
  text?: string;
} & AvatarProps &
  RefAttributes<HTMLSpanElement>;

const colors = [
  { color: '#f56a00', backgroundColor: '#fde3cf' },
  { color: '#ff7875', backgroundColor: '#ffccc7' },
  { color: '#faad14', backgroundColor: '#fff1b8' },
  { color: '#bae637', backgroundColor: '#f4ffb8' },
  { color: '#52c41a', backgroundColor: '#d9f7be' },
  { color: '#1677ff', backgroundColor: '#bae0ff' },
  { color: '#eb2f96', backgroundColor: '#ffd6e7' },
  { color: '#2f54eb', backgroundColor: '#d6e4ff' },
  { color: '#722ed1', backgroundColor: '#efdbff' },
  /* { color: '', backgroundColor: '' },
  { color: '', backgroundColor: '' },
  { color: '', backgroundColor: '' },
  { color: '', backgroundColor: '' },
  { color: '', backgroundColor: '' }, */
];

const getColorIndex = (str: string) => {
  const code = str.charCodeAt(0) | str.charCodeAt(1); /*  + Math.floor(Math.random() * 10) */
  return code % colors.length;
};

const SAvatar: React.FC<SAvatarProps> = (props) => {
  const color = colors[getColorIndex(props.text || '')];
  return (
    <Avatar style={{ ...props.style, ...color }} {...props}>
      <span title={props.alt || ''}>{props.text || ''}</span>
    </Avatar>
  );
};
export default SAvatar;
