﻿import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { message, notification, Typography } from 'antd';
import { LS_TOKEN_NAME } from './constants';
import Util, { notifySuccess } from './util';
import { history } from 'umi';
import { stringify } from 'querystring';

// Error handling scheme: Error type
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}
// The response data format agreed with the backend
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
  showType?: ErrorShowType;
}

/**
 * @name Error handling
 * Pro comes with error handling, you can make your own changes here
 * @doc https://umijs.org/docs/max/request#Configuration
 */
export const errorConfig: RequestConfig = {
  // Error handling: umi@3's error handling solution.
  errorConfig: {
    // Error thrown
    errorThrower: (res) => {
      const { success, data, errorCode, errorMessage, showType } =
        res as unknown as ResponseStructure;

      if (!success) {
        const error: any = new Error(errorMessage);
        error.name = 'BizError';
        error.info = { errorCode, errorMessage, showType, data };
        throw error; //Throw your own error
      }
    },
    // Error reception and processing
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // The error thrown by our errorThrower.
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              // do nothing
              break;
            case ErrorShowType.WARN_MESSAGE:
              message.warning(errorMessage);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              message.error(errorMessage);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: errorMessage,
                message: errorCode,
              });
              break;
            case ErrorShowType.REDIRECT:
              // TODO: redirect
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        // Axios error
        // The request was sent successfully and the server responded with a status code, but the status code is outside the 2xx range
        // message.error(`Response status:${error.response.status}`);
        console.log(error.response.data);
        if (403 === error.response.data?.code) {
          localStorage.setItem(LS_TOKEN_NAME, '');
          history.replace({
            pathname: '/user/login',
            search: stringify({
              redirect: location.pathname + location.search,
            }),
          });
        }
      } else if (error.request) {
        // The request was successfully initiated, but no response was received
        // \`error.request\` is an instance of XMLHttpRequest in the browser,
        // and an instance of http.ClientRequest in node.js
        message.error('None response! Please retry.');
      } else {
        // Something went wrong while sending the request
        message.error('Request error, please retry.');
      }
    },
  },

  // Request interceptor
  requestInterceptors: [
    (config: RequestOptions) => {
      //Intercept request configuration for personalized processing.
      const token = localStorage.getItem(LS_TOKEN_NAME);
      const authHeader = config.headers || {};
      if (token /*  && (options.method !== 'GET' || options.widthToken) */) {
        // @ts-ignore
        authHeader.Authorization = `Bearer ${token}`;
      }

      return {
        ...config,
        headers: authHeader,
        options: { interceptors: true },
      };

      // const url = config?.url?.concat('?token = 123');
      // return { ...config, url };
    },
  ],

  // Response interceptor
  responseInterceptors: [
    (response) => {
      //Intercept response data and perform personalized processing
      const { data } = response as unknown as ResponseStructure;
      // console.log('[responseInterceptors]', data);

      if (data?.status !== 'success') {
        message.error('Request failed!');
      }

      const flashMsg = (response.data as any).messageFlash;
      if (flashMsg) {
        // eslint-disable-next-line guard-for-in
        for (const key in flashMsg) {
          const msgStr = (
            <ul className="msg">
              {flashMsg[key].map((x: any, ind: number) => {
                const subMsgList = x.split('\n');
                if (subMsgList.length > 1) {
                  return (
                    <li key={ind}>
                      {subMsgList.map((y: any, yind: number) => (
                        <div key={yind}>{y}</div>
                      ))}
                    </li>
                  );
                } else return <li key={ind}>{x}</li>;
              })}
            </ul>
          );
          if (key == 'w') {
            notification.warning({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Warnings
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          } else if (key == 'e') {
            notification.error({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Errors
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          } else if (key == 's') {
            notifySuccess(msgStr);
          } else if (key == 'i') {
            notification.info({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Information
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          }
        }
      }
      return response;
    },
  ],
};
