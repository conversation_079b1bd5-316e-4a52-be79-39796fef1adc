/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/sys/trademark';

/** 
 * Get all product categories
 * 
 * GET /api/sys/trademark */
export async function getTrademarkList(
  params: API.PageParams & Partial<API.Trademark>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Trademark>> {
  return request<API.Result<API.Trademark>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export async function getTrademarkACList(params: API.PageParams & Partial<API.Trademark>) {
  return getTrademarkList({ ...params, pageSize: 300 }).then(res => {
    return res.data?.map(x => ({ ...x, value: x.id, label: x.name }) as RequestOptionsType)
  })
}

/** Create a product category
 * 
 *  POST /api/sys/trademark */
export async function addTrademark(data: API.Trademark | FormData, options?: { [key: string]: any }): Promise<API.Trademark> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.Trademark>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** 
 * 
 * Update product category 
 * 
 * PUT /api/sys/trademark/{id} */
export async function updateTrademark(id: number, data?: API.Trademark | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Trademark>>(`${urlPrefix}/${id}`, config).then((res) => res.message);
}


/** delete DELETE /api/sys/trademark/{id} */
export async function deleteTrademark(id?: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
