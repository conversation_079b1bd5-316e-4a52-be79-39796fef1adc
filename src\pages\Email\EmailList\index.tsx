import { But<PERSON>, message, Drawer, Card, Tag, Typography, Space } from 'antd';
import type { CSSProperties } from 'react';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { sn } from '@/util';
import { getEmailList, deleteEmail, dsPullEmail, updateEmail } from '@/services/app/Email/email';
import { DictCode, DictType } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import {
  DownloadOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  SendOutlined,
} from '@ant-design/icons/lib/icons';
import ViewEmail from './components/ViewEmail';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { Link, useLocation, useModel } from 'umi';
import UpdateForm from './components/UpdateForm';
import ReplyForm from './components/ReplyForm';
import EmailBoxTypeIcon from './components/EmailBoxTypeIcon';
import AttachmentIconIcon from './components/AttachmentIcon';
import CreateEmailForm from './components/CreateEmailForm';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Email[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEmail({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const EmailList: React.FC = () => {
  const location: any = useLocation();
  const { getDictByCode, getDictOptionsCV } = useModel('app-settings');
  const crmStatus2Color = getDictByCode(DictCode.EMAIL_BG_BY_CRM_CASE_STATUS, 'casted_value') ?? {};

  // const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Email>();
  const [selectedRowsState, setSelectedRows] = useState<API.Email[]>([]);

  // Search
  const searchFormRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState<boolean>(false);

  const handlePullEmails = () => {
    const hide = message.loading('Downloading emails...', 0);
    dsPullEmail({})
      .then((res) => {
        actionRef.current?.reload();
        message.success('Downloaded the latest emails successfully.');
      })
      .catch(Util.error)
      .finally(() => hide());
  };

  useEffect(() => {
    if (location.query?.order_id) {
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldsValue({ includeHidden: true, boxes: [], ...location.query });
      actionRef.current?.reload();
    }
  }, [location.query]);

  const columns: ProColumns<API.Email>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60,
      search: false,
      align: 'center',
      className: 'c-grey text-sm',
    },
    {
      title: '',
      dataIndex: 'action-hide',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              updateEmail({ id: record.id, is_hidden: record.is_hidden ? 0 : 1 })
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch(Util.error);
            }}
          >
            {!record.is_hidden ? (
              <EyeInvisibleOutlined
                style={{ color: '#ff8686' }}
                title="Make this email invisible."
              />
            ) : (
              <EyeOutlined title="Make this email visible." />
            )}
          </Typography.Link>
        );
      },
    },
    {
      title: '',
      dataIndex: 'box',
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(__, record) {
        return <EmailBoxTypeIcon box={record.box} />;
      },
    },
    {
      title: 'From',
      dataIndex: 'sender',
      sorter: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: 'To',
      dataIndex: 'receiver',
      sorter: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      sorter: true,
      ellipsis: true,
      hideInSearch: false,
      width: 300,
      render: (dom, record) => (
        <>
          <Link to={`/email/detail/${record.id}`} target="_blank">
            {record.subject ? record.subject : ' - '}
          </Link>
        </>
      ),
    },
    {
      title: '',
      dataIndex: 'attachments',
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(__, record) {
        return <AttachmentIconIcon id={record.id} attachments={record.attachments} />;
      },
    },
    {
      title: 'Body',
      dataIndex: 'text_plain',
      sorter: true,
      ellipsis: true,
      width: 300,
      render: (dom, record) => (
        <>
          <Link to={`/email/detail/${record.id}`} target="_blank">
            {record.text_plain ? record.text_plain : ' - '}
          </Link>
        </>
      ),
    },
    {
      title: 'Date',
      sorter: true,
      dataIndex: 'date',
      valueType: 'dateTime',
      search: false,
      width: 100,
      defaultSortOrder: 'descend',
      className: 'c-grey text-sm',
      ellipsis: true,
      render: (dom, record) => Util.dtToDMYHHMM(record.date),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 100,
      search: false,
      ellipsis: true,
      render: (dom, record) =>
        record.status ? (
          <Tag style={{ fontSize: 10 }}>{getDictByCode(record.status) ?? dom}</Tag>
        ) : null,
    },
    {
      title: 'Mail ID',
      dataIndex: 'mail_id',
      width: 60,
      search: false,
      align: 'center',
      className: 'c-grey text-sm',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 70,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
          title="Update"
        >
          <EditOutlined />
        </a>,
        <a
          key="reply"
          onClick={() => {
            handleReplyModalVisible(true);
            setCurrentRow(record);
          }}
          title="Reply"
        >
          <SendOutlined />
        </a>,
        <a
          key={'view'}
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          <EyeOutlined />
        </a>,
      ],
    },
  ];

  return (
    <PageContainer
      title={
        <Space size={24}>
          <div>Emails</div>
          <div>
            <Button variant="solid" color="green" onClick={() => handleCreateModalVisible(true)}>
              New Mail
            </Button>
          </div>
        </Space>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.Email & { linkedType?: number | string }>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_email_grid', {
            linkedType: 1,
          })}
          submitter={{
            submitButtonProps: {
              loading: loading,
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'sender'} label="From" width={180} placeholder={'From'} />
          <ProFormText name={'receiver'} label="To" width={180} placeholder={'To'} />
          <ProFormText name={'subject'} label="Subject" width={180} placeholder={'Subject'} />
          <ProFormText name={'order_id'} label="Order ID" width={100} placeholder={'Order ID'} />
          <ProFormText
            name={'increment_id'}
            label="Increment ID"
            width={150}
            placeholder={'Increment ID'}
          />
          <ProFormText name={'name'} label="Name" width={200} placeholder={'Name in Order'} />
          <ProFormText
            name={'ebay_order_id'}
            label="Ebay/KL Order"
            width={150}
            placeholder={'Ebay/KL Order'}
          />
          <ProFormSelect
            name="boxes"
            label="Box"
            width={150}
            mode="multiple"
            placeholder={'Box'}
            initialValue={['INBOX']}
            options={[
              { value: 'INBOX', label: 'Inbox' },
              { value: 'SENT', label: 'Sent' },
            ]}
          />
          <ProFormCheckbox
            name="includeHidden"
            label="Show hidden?"
            valuePropName="checked"
            getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}
            initialValue={0}
          />
          <ProFormCheckbox
            name="onlyNew"
            label="Only New"
            valuePropName="checked"
            getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}
            initialValue={1}
          />
        </ProForm>
      </Card>
      <ProTable<API.Email, API.PageParams>
        headerTitle={'Email list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handlePullEmails();
            }}
          >
            <DownloadOutlined /> Sync
          </Button>,
        ]}
        scroll={{ x: 800 }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_email_grid_p')?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_email_grid', searchFormValues);
          Util.setSfValues('sf_email_grid_p', params);
          setLoading(true);
          return getEmailList(
            {
              ...params,
              ...searchFormValues,
              with: 'crmCase,parcel,orderDetail',
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        onRow={(record) => {
          let rowCls = '';
          const style: CSSProperties = {};
          return {
            style,
            className: rowCls,
          };
        }}
        columnEmptyText=""
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'email'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <BatchDeleteAction
            title="email"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <ReplyForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);
          handlePullEmails();
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <CreateEmailForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async (value) => {
          handlePullEmails();
        }}
        onCancel={() => {
          handleCreateModalVisible(false);
        }}
      />

      <Drawer
        width={'50%'}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && <ViewEmail email={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EmailList;
