import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/supplier/supplier-group';

/**
 * Get Magento Supplier groups list
 *
 * GET /api/supplier/supplier-group
 */
export async function getSupplierGroupListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return [];
  /* return request<API.ResultObject<API.PaginatedResult<any>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  })); */
}

/**
 * get GET /api/offer/ac-list
 *
 * get the autocomplete lists.
 *
 */
export async function getSupplierGroupACList(params: { [key: string]: string }, sort?: any) {
  return getSupplierGroupListByPage(params).then((res) => {
    return res.data.map((x: API.SupplierGroup) => ({
      ...x,
      value: x.id,
      label: `${x.code || '-'} ${x.tax_class_name ? ` | ${x.tax_class_name}` : ''}`,
    }));
  });
}

/**
 * Down syncing
 *
 * PUT /api/supplier/supplier-group/dsSupplierGroup
 */
export async function dsSupplierGroup(options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/dsSupplierGroup`, {
    method: 'PUT',
    ...(options || {}),
  });
}
