import type { Dispatch, LegacyRef, SetStateAction } from 'react';
import React, { useEffect, useMemo, useRef } from 'react';
import { Button, Col, Input, InputRef, message, Popover, Row, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { updateEmailTemplate } from '@/services/app/Email/email-template';
import Util from '@/util';
import { ProForm, ProFormCheckbox, ProFormDigit } from '@ant-design/pro-components';
import HtmlEditor from '@/components/HtmlEditor';
import {
  getEmailTemplateCatACList,
  getEmailTemplateCategoryList,
} from '@/services/app/Email/email-template-category';
import { useModel } from '@umijs/max';
import { sendEmail } from '@/services/app/Email/email';
import { DictCode } from '@/constants';
import { InfoCircleOutlined } from '@ant-design/icons';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEmailTemplate(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.EmailTemplate>;

export type UpdateFormProps = {
  initialValues?: Partial<API.EmailTemplate>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplate) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible } = props;

  // test email functionality.
  const { initialState } = useModel('@@initialState');
  const { getDictByCode } = useModel('app-settings');
  const defaultEmailFrom = useMemo(() => getDictByCode(DictCode.EMAIL_FROM), [getDictByCode]);
  const inputRef = useRef<LegacyRef<InputRef>>();

  // main biz logic
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(initialValues || {}) };

      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, initialValues]);

  return (
    <ModalForm
      title={
        <Row>
          <Col flex="auto">Update Email Template</Col>
          <Col flex="300px">
            <Space.Compact>
              <Input
                ref={inputRef as LegacyRef<InputRef>}
                defaultValue={initialState?.currentUser?.email || ''}
                width={180}
              />
              <Button
                type="primary"
                onClick={() => {
                  if (!defaultEmailFrom) {
                    message.error('Default From email is not configured in sys configuration.');
                    return;
                  }

                  const testEmail = (inputRef.current as unknown as InputRef)?.input?.value;
                  const formValues = formRef.current?.getFieldsValue();

                  const hide = message.loading('Sending a test Email to you...', 0);
                  sendEmail({
                    sender: defaultEmailFrom,
                    receiver: testEmail,
                    subject: formValues.subject,
                    text_html: formValues.text_html,
                  })
                    .then((res) => {
                      // message.success("")
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
              >
                Test to me
              </Button>
            </Space.Compact>
          </Col>
        </Row>
      }
      width="1000px"
      open={props.modalVisible}
      onOpenChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      grid
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <Col span={14}>
        <ProFormText
          width="lg"
          name="title"
          label="Template Title"
          rules={[
            {
              required: true,
              message: 'Title is required',
            },
          ]}
          required
        />
        <ProFormText width="lg" name="subject" label="Subject" allowClear />
      </Col>
      <Col span={8}>
        <ProFormSelect
          name="category_id"
          label="Category"
          request={(params) => {
            return getEmailTemplateCatACList(params);
          }}
          allowClear
          showSearch
        />
        <Row>
          <Col span={12}>
            <ProFormDigit width="xs" name="sort" label="Sort" />
          </Col>
          <Col span={12}>
            <ProFormCheckbox name="gen_sort" label="Generate a sort No?" initialValue={0} />
          </Col>
        </Row>
      </Col>
      <ProForm.Item
        name={'text_html'}
        label={
          <>
            Template&nbsp;
            <Popover
              title="Usable keywords"
              content={
                <>
                  <Typography.Text copyable={{ text: '%salutation%' }}>
                    <b>%salutation%</b>
                    {` e.g. Dear {firstName},`}
                  </Typography.Text>
                </>
              }
            >
              <InfoCircleOutlined />
            </Popover>
          </>
        }
        style={{ width: '100%' }}
        labelCol={undefined}
        wrapperCol={{ span: 24 }}
      >
        <HtmlEditor
          id={`email_body_update`}
          initialFocus
          enableTextModule
          hideMenuBar
          toolbarMode={2}
          height={400}
        />
      </ProForm.Item>
    </ModalForm>
  );
};

export default UpdateForm;
