import Util from '@/util';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormList, ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';

export type CaseReasonTextUpdateModalFormProps = {
  initialValues?: Partial<API.CrmCase>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.CrmCase) => Promise<boolean | void>;
};

const CaseReasonTextUpdateModalForm: React.FC<CaseReasonTextUpdateModalFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const priceActionRef = useRef<FormListActionType>();

  useEffect(() => {
    if (props.modalVisible && formRef && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.modalVisible, formRef, props.initialValues]);

  return (
    <ModalForm<API.CrmCase>
      title={'Update Damage reasons for Case #' + props.initialValues?.id}
      width="800px"
      size="small"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      formRef={formRef}
      onFinish={async (value) => {
        if (props.onSubmit)
          props.onSubmit(value).then((res) => {
            props.handleModalVisible(false);
          });
      }}
    >
      <ProFormList
        actionRef={priceActionRef}
        key={'uid'}
        name="reason_text"
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: 'Add a reason',
        }}
        creatorRecord={(index: number) => {
          return {
            uid: Util.genNewKey(),
          };
        }}
        deleteIconProps={{ tooltipText: 'Remove' }}
        copyIconProps={{ tooltipText: 'Copy row' }}
      >
        <ProFormGroup size={'small'}>
          <ProFormText
            placeholder={'SKU'}
            label="SKU"
            name={'sku'}
            rules={[
              {
                required: true,
                message: '',
              },
            ]}
            width="sm"
            className="w-full"
          />
          <ProFormTextArea
            name={'reason_text'}
            label="Reason"
            className="w-full"
            placeholder={'Reason'}
            width={'lg'}
            fieldProps={{ rows: 2 }}
          />
        </ProFormGroup>
      </ProFormList>
    </ModalForm>
  );
};

export default CaseReasonTextUpdateModalForm;
