import { getSupplierContactListByPage } from '@/services/app/Supplier/supplier-contact';
import Util from '@/util';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Card, Col, Modal, Row, Space } from 'antd';
import { Dispatch, SetStateAction, useRef, useState } from 'react';
import { DefaultContactIconComp } from '..';

export type SupplierSelectModalSearchParamsType = {
  group_id?: number;
};

export type SearchFormValueType = Partial<API.SupplierContact> & API.PageParams;

type RowType = API.SupplierContact;

type SupplierSelectModalProps = {
  searchParams?: SupplierSelectModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  addSupplierContacts: (selectedRows: RowType[]) => void;
};

const SupplierSelectModal: React.FC<SupplierSelectModalProps> = (props) => {
  const {
    searchParams,
    modalVisible,
    handleModalVisible,
    addSupplierContacts: addSupplierContacts,
  } = props;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RowType>();
  const [selectedRows, setSelectedRows] = useState<RowType[]>([]);

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Supplier',
      dataIndex: ['supplier', 'name'],
      width: 170,
      showSorterTooltip: false,
      copyable: true,
      render(dom, entity) {
        return (
          <Row>
            <Col flex="auto">{dom}</Col>
            <Col flex="24px">
              <DefaultContactIconComp is_default={entity.is_default} />
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Email',
      dataIndex: 'email',
      width: 170,
      showSorterTooltip: false,
      copyable: true,
    },
    {
      title: 'Name',
      dataIndex: 'fullname',
      width: 170,
      showSorterTooltip: false,
    },
  ];

  return (
    <Modal
      title={<>Suppliers Contacts List</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="900px"
      footer={false}
      styles={{ body: { paddingTop: 0 } }}
    >
      <Card variant="borderless">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormText
            name={'keyWords'}
            label="KeyWords"
            width={140}
            placeholder={'Email / Name / Contact Names'}
          />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Supplier Contacts List</span>
          </Space>
        }
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: 20,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('cu_sf_supplier_m', searchFormValues);
          Util.setSfValues('cu_sf_supplier_m_p', params);

          setLoading(true);
          return getSupplierContactListByPage(
            {
              ...searchFormValues,
              ...params,
              with: 'supplier',
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
        tableAlertOptionRender={(alertProps) => {
          return (
            <Space size={8}>
              <Button
                type="primary"
                onClick={() => {
                  addSupplierContacts(selectedRows);
                  alertProps.onCleanSelected();
                }}
              >
                Add
              </Button>
              <Button type="link" onClick={alertProps.onCleanSelected}>
                Clear
              </Button>
            </Space>
          );
        }}
      />
    </Modal>
  );
};

export default SupplierSelectModal;
