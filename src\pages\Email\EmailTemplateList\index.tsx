import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, message, Typography } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sEllipsed, sLeftPad, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getEmailTemplateList, deleteEmailTemplate } from '@/services/app/Email/email-template';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import { ProForm, ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { getEmailTemplateCatACList } from '@/services/app/Email/email-template-category';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.EmailTemplate[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEmailTemplate({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const EmailTemplateList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<API.EmailTemplate>>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.EmailTemplate>();
  const [selectedRowsState, setSelectedRows] = useState<API.EmailTemplate[]>([]);

  const columns: ProColumns<API.EmailTemplate>[] = [
    {
      title: 'Category',
      dataIndex: ['cat1_mix'],
      width: 150,
      sorter: true,
      // defaultSortOrder: 'ascend',
      render(__, entity) {
        return `${entity.category?.cat1}${sLeftPad(entity.sort, 3, '0')}`;
      },
    },
    {
      title: 'Template Title',
      dataIndex: 'title',
      sorter: true,
      width: 200,
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      sorter: true,
      width: 200,
    },
    {
      title: 'Text',
      dataIndex: 'text_html',
      width: 600,
      render(__, entity) {
        return sEllipsed(Util.stripTags(entity.text_html), 300);
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 90,
    },
    {
      title: 'Created On',
      dataIndex: 'created_on',
      width: 110,
      sorter: true,
      defaultSortOrder: 'descend',
      render(__, entity) {
        return Util.dtToDMYHHMM(entity.created_on);
      },
    },
    {
      title: 'Updated On',
      dataIndex: 'updated_on',
      width: 110,
      render(__, entity) {
        return Util.dtToDMYHHMM(entity.updated_on);
      },
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer
      extra={<Typography.Link href="/email/template-category">Categories</Typography.Link>}
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.EmailTemplate>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('cu_sf_email_template', {})}
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.setFieldsValue({});
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name="category_id"
            label="Category"
            request={(params) => {
              return getEmailTemplateCatACList(params);
            }}
            allowClear
            showSearch
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            width="sm"
          />
          <ProFormText
            width="md"
            name="keyWords"
            label="Keywords"
            placeholder="Category / Title / Subject"
            allowClear
          />
        </ProForm>
      </Card>

      <ProTable<API.EmailTemplate, API.PageParams>
        headerTitle={'Email Template list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('cu_sf_email_template_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('cu_sf_email_template', searchFormValues);
          Util.setSfValues('cu_sf_email_template_p', params);
          return getEmailTemplateList(
            { ...params, with: 'category', ...searchFormValues },
            sort,
            filter,
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'Email template'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <BatchDeleteAction
            title="Email template"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default EmailTemplateList;
