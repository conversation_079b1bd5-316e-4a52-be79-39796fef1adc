import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { AutoCompleteProps, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateEmailTemplateCategory } from '@/services/app/Email/email-template-category';
import Util from '@/util';
import { ProFormCheckbox, ProFormDigit } from '@ant-design/pro-components';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEmailTemplateCategory(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.EmailTemplateCategory>;

export type UpdateFormProps = {
  initialValues?: Partial<API.EmailTemplateCategory>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplateCategory) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible } = props;
  const formRef = useRef<ProFormInstance>();

  const [options, setOptions] = useState<AutoCompleteProps['options']>([]);

  useEffect(() => {
    if (modalVisible && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(initialValues || {}) };

      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, initialValues]);

  return (
    <ModalForm
      title={'Update Email Template Category'}
      width="600px"
      open={props.modalVisible}
      onOpenChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        width="lg"
        name="cat1"
        label="Category Name"
        rules={[
          {
            required: true,
            message: 'Category Name is required',
          },
        ]}
        required
      />

      <ProFormDigit width="xs" name="sort" label="Sort" />

      <ProFormCheckbox name="gen_sort" label="Generate a sort No?" initialValue={0} />
    </ModalForm>
  );
};

export default UpdateForm;
