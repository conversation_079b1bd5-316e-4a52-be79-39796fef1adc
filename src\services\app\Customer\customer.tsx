import { request } from "umi";
import { paramsSerializer } from "../api";
import { RequestConfig } from "@umijs/max";

const urlPrefix = "/api/customer/customer";

/**
 * Get Customers list
 *
 * GET /api/customer/customer
 */
export async function getCustomerListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Customer> & { productCategoryKv?: Record<number, string> }>>(`${urlPrefix}`, {
    method: "GET",
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getCustomer(id?: number, params?: API.Customer & API.PageParams) {
  return getCustomerListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Get Magento Customer x Email matrix
 *
 * GET /api/customer/customer/email-matrix
 */
export async function getCustomer2EmailMatrix(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Customer> & { templates?: API.EmailTemplate[] }>>(`${urlPrefix}/email-matrix`, {
    method: "GET",
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
    templates: res.message.templates, // For total row pagination hack.
  }));
}

/**
 * Down syncing
 *
 * PUT /api/customer/customer/assignCustomersToOffer
 */
export async function assignCustomersToOffer(
  offerNo?: string | number,
  customerIds?: number[],
  mode?: "add" | "remove",
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/assignCustomersToOffer`, {
    method: "PUT",
    data: { offerNo, customerIds, mode },
    ...(options || {}),
  });
}

/**
 * Down syncing
 *
 * PUT /api/customer/customer/dsCustomer
 */
export async function dsCustomer(options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/dsCustomer`, {
    method: "PUT",
    ...(options || {}),
  });
}

/**
 * Create customer.
 *
 *  POST /api/customer/customer */
export async function addCustomer(data?: API.Customer | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: "POST",
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Customer>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update Customer extension data.
 *
 *  PUT /api/customer/customer/{id}/update */
export async function updateCustomer(id?: number, data?: API.Customer | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: "PUT",
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Customer>>(`${urlPrefix}/${id}/update`, config).then((res) => res.message);
}

/** delete DELETE /api/customer/customer/{id} */
export async function deleteCustomer(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: "DELETE",
    ...(options || {}),
  });
}
