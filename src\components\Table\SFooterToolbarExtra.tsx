import type { ActionType } from '@ant-design/pro-table';
import { Space } from 'antd';

const SFooterToolbarExtra: React.FC<{
  title: string;
  selectedRowsState: any[];
  actionRef: React.MutableRefObject<ActionType | undefined>;
}> = ({ selectedRowsState, actionRef, title }) => {
  return (
    <Space>
      <span>
        Chosen &nbsp;<a>{selectedRowsState.length}</a>
        &nbsp;{title ?? 'row'}s.
      </span>
      <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
    </Space>
  );
};

export default SFooterToolbarExtra;
