import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/supplier/supplier-address';

/**
 * Get Magento Suppliers list
 *
 * GET /api/supplier/supplier-address
 */
export async function getSupplierAddressListByPage(
  params: API.PageParams,
  sort?: any,
  filter?: any,
) {
  return request<API.ResultObject<API.PaginatedResult<API.MagSupplierAddress>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}
