import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import type { Editor as TinyMCEEditor } from 'tinymce';
import type { FormItemProps } from 'antd';
import { getSysTextModuleACList } from '@/services/app/Sys/text-module';
import { isNaN } from 'lodash';
import { nl2br } from '@/util';

const isFinishMode = (strP: any) => {
  const str = `${strP ?? ''}`;
  const lastCh = str[str.length - 1] || '';
  return /* lastCh == ' ' ||  */ lastCh == '#';
};

// editor
/* const toTinyMceValue = (htmlValue: string) => {
  let value: string = htmlValue || '';
  let nStartBody: number = -1;
  let nEndBody: number = -1;

  nStartBody = value.indexOf('<body>');
  nEndBody = value.indexOf('</body>');
  if (nStartBody !== -1) {
    value = value.substring(nStartBody + 6, nEndBody - 1);
  }

  return value;
}; */

type IHtmlEditorProps = {
  className?: string;
  placeholder?: string;
  height?: number;
  onChangeHtml?: (newHtml: string) => void;
  initialFocus?: boolean;
  enableTextModule?: boolean;
  hideMenuBar?: boolean;

  // auto completion extension feature
  extEditorRef?: React.MutableRefObject<TinyMCEEditor | null>;
  acName?: string;
  setLoading?: Dispatch<SetStateAction<boolean>>;

  onChange?: any;
  value?: any;
  toolbarMode?: number;
} & FormItemProps;

const HtmlEditor = (props: IHtmlEditorProps) => {
  // onChange, value, id are from antd.
  const {
    id,
    onChange,
    value,

    // placeholder,
    // className,
    height,
    onChangeHtml,
    initialValue,
    initialFocus,
    enableTextModule,
    hideMenuBar,
    extEditorRef,
    acName,
    setLoading,
  } = props;

  // we disable internal Ref Object. Recommended to use extEditorRef
  // const editorRef = useRef<TinyMCEEditor | null>(null);
  /* const log = () => {
    if (editorRef.current) {
      console.log('[content ]', editorRef.current.getContent());
    }
  }; */

  const keyWordRef = useRef<string>('');

  const handleChange = (newTextParam: string) => {
    onChange?.(newTextParam);
    onChangeHtml?.(newTextParam);
    // console.log('===> ON change: ', newTextParam);
  };

  return (
    <Editor
      id={id}
      inline={false}
      tagName={`div`}
      initialValue={initialValue}
      value={value || ''}
      tinymceScriptSrc={`${API_URL}/assets/js/tinymce-6/tinymce.min.js`}
      onInit={(evt: any, editor: any) => {
        // editorRef.current = editor;
        if (extEditorRef) {
          extEditorRef.current = editor;
        }
        if (initialFocus) {
          editor.focus();
        }
        if (enableTextModule) {
          editor.ui.registry.addAutocompleter(acName ?? 'acName', {
            ch: '#',
            fetch: async (
              pattern: string,
              maxResults: number,
              fetchOptions: Record<string, any>,
            ): Promise<any[]> => {
              /* {
                type: 'autocompleteitem',
                value: '!loading!',
                text: 'Loading...',
              }
               */
              // console.log(`[${pattern}]`, maxResults, fetchOptions);
              keyWordRef.current = pattern;
              setLoading?.(true);
              // editor.setProgressState(true);
              return new Promise((resolve, reject) => {
                let exactKeyWord = null;
                let keyWord: any = pattern;
                if (isFinishMode(pattern)) {
                  exactKeyWord = pattern.substring(0, pattern.length - 1);
                  // console.log(exactKeyWord);
                  if (isNaN(Number(exactKeyWord))) {
                    exactKeyWord = null;
                  } else {
                    // exact model
                    keyWord = null;
                  }
                  // console.log(exactKeyWord);
                }
                getSysTextModuleACList({ keyWord: keyWord, numberExact: Number(exactKeyWord) })
                  .then((res) => {
                    let oldContent = editor.getContent();
                    if (exactKeyWord && res.length == 1) {
                      // console.log('--> replacing...', `[#${exactKeyWord}]`, `[#${pattern}]`, res[0].text);
                      oldContent = oldContent.replaceAll(`#${pattern}`, res[0].text);
                      editor.setContent(oldContent);
                    }
                    resolve(res.map((x) => ({ ...x, value: x.text, type: 'autocompleteitem' })));
                  })
                  .catch((err) => reject());
              }).finally(() => {
                setLoading?.(false);
                // editor.setProgressState(false);
              }) as unknown as any[];
            },
            onAction: (api: any, rng: any, v: any, meta: any) => {
              // console.log(' --> api', api, 'rng', rng, v, meta);
              if (v !== '!loading!') {
                editor.selection.setRng(rng);
                editor.insertContent(nl2br(v));
                api.hide();
                keyWordRef.current = '';
              }
            },
          });
        }
      }}
      init={{
        relative_urls: false,
        force_br_newlines: false,
        // forced_root_block: false,
        // forced_root_block: '',
        entity_encoding: 'raw',
        language: 'de',
        smart_paste: true,
        // file_picker_types: 'image',
        paste_data_images: true, // allow image paste
        // menubar: false,
        menubar: hideMenuBar ? false : 'file edit view insert format tools table help',
        toolbar_sticky: true,
        toolbar_mode: 'sliding',
        toolbar:
          props.toolbarMode == 2
            ? 'bold italic underline strikethrough | forecolor backcolor | fontsize fontfamily | alignleft aligncenter alignright alignjustify | bullist numlist link | code preview fullscreen'
            : 'bold italic underline strikethrough | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist link | code preview fullscreen',

        font_size_formats: '8pt 10pt 12pt 14pt 18pt 24pt 36pt 48pt 60pt',
        branding: false,
        plugins: 'link lists advlist autoresize autolink preview fullscreen code wordcount',
        height: height ?? 200,
        min_height: height ?? 200,
        max_height: Math.max(height || 0, 400),
        toolbar_location: 'top',
        content_style: 'body { font-family:Inter,Arial,sans-serif; font-size:13px }',
        skin: 'oxide',
        // icons: 'default',
      }}
      onEditorChange={handleChange}
    />
  );
};

export default HtmlEditor;
