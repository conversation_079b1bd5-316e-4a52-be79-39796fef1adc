import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addTrademark } from '@/services/app/Sys/trademark';
import { message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.Trademark) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addTrademark(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.Trademark>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
    }
  }, [modalVisible]);

  return (
    <ModalForm
      title={'New Trademark'}
      width="500px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Trademark);
        if (success) {
          formRef.current?.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        width="xl"
        name="name"
        label="Name"
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
      />
    </ModalForm>
  );
};

export default CreateForm;
