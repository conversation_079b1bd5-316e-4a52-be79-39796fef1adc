import { DictCode } from '@/constants';
import { getAppSettings } from '@/services/app/api';
import Util from '@/util';
import { useCallback, useEffect, useMemo, useState } from 'react';

const defaultAppSetting: API.AppSettings = {
  dict: {},
  drSelection: {},
};

export default () => {
  const [appSettings, setAppSettings] = useState<API.AppSettings>(defaultAppSetting);
  const [gSearch, setGSearch] = useState<Record<string, any>>({});

  const loadAppSetting = useCallback(() => {
    getAppSettings()
      .then((res) => setAppSettings(res))
      .catch(() => Util.error('Failed to fetch app settings. Please try to reload a page!'));
  }, []);

  useEffect(() => {
    loadAppSetting();
  }, [loadAppSetting])

  const getDictOptions = useCallback((type: string, orderBy?: string) => {
    const dict = appSettings.dict || {};
    const keys = Object.keys(dict);
    let result = keys
      .filter((k) => dict[k].type === type)
      .map((k) => ({
        ...dict[k],
        label: dict[k].value,
        value: dict[k].value,
      }));
    if (orderBy) {
      result = result.sort((a: any, b: any) => (a[orderBy] < b[orderBy] ? -1 : a[orderBy] > b[orderBy] ? 1 : 0));
    }

    return result;
  }, [appSettings.dict]);

  const getDictOptionsCV = useCallback(
    (type: string, orderBy?: string, orderDir?: 'asc' | 'desc', labelField = 'value') => {
      const dict = appSettings.dict || {};
      const keys = Object.keys(dict);
      let result = keys
        .filter((k) => dict[k].type === type)
        .map((k) => ({
          ...dict[k],
          label: dict[k][labelField],
          value: dict[k].code,
        }));
      if (orderBy) {
        result = result.sort((a: any, b: any) => (a[orderBy] < b[orderBy] ? -1 : a[orderBy] > b[orderBy] ? 1 : 0));
      }

      return result;
    },
    [appSettings.dict],
  );

  const getDictByCode = useCallback(
    (code: string, key = 'value') => {
      const dict = appSettings.dict || {};
      if (!code) return undefined;
      return !key ? dict[`${code}`] : dict[`${code}`]?.[key];
    },
    [appSettings.dict],
  );

  return {
    appSettings,
    setAppSettings,
    gSearch,
    setGSearch,
    loadAppSetting,
    getDictOptions,
    getDictOptionsCV,
    getDictByCode,
  };
};
