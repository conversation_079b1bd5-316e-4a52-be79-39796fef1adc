import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/customer/customer-contact';

/**
 * Get Customer contacts list
 *
 * GET /api/customer/customer-contact
 */
export async function getCustomerContactListByPage(
    params: API.PageParams,
    sort?: any,
    filter?: any,
) {
    return request<API.ResultObject<API.PaginatedResult<API.CustomerContact>>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        pagination: res.message.pagination, // For total row pagination hack.
    }));
}
