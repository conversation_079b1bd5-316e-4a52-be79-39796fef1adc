import { DT_FORMAT_DMY, DT_FORMAT_MY } from '@/constants';
import Util from '@/util';
import type { ProFormItemProps } from '@ant-design/pro-form';
import { ProFormDatePicker } from '@ant-design/pro-form';
import type { DatePickerProps } from 'antd';

import locale from 'antd/locale/de_DE';
// import dayjs from 'dayjs';

const SDatePicker = (props: ProFormItemProps<DatePickerProps, any>) => {
  return (
    <ProFormDatePicker
      {...props}
      fieldProps={{
        ...(props?.fieldProps || {}),
        locale: locale.DatePicker,
        format: props?.fieldProps?.picker === 'month' ? DT_FORMAT_MY : DT_FORMAT_DMY,
      }}
      getValueFromEvent={(value: any) =>
        props?.fieldProps?.picker === 'month'
          ? Util.dtToYMD(value, DT_FORMAT_MY)
          : Util.dtToYMD(value)
      }
    />
  );
};

export default SDatePicker;
