import { DeleteOutlined } from '@ant-design/icons';
import { Button, Popconfirm } from 'antd';

const BatchDeleteAction: React.FC<{
  title?: string;
  onConfirm?: any;
}> = ({ title, onConfirm, ...rest }) => {
  return (
    <Popconfirm
      title={<>Are you sure you want to delete selected {title ?? 'row'}s?</>}
      okText="Yes"
      cancelText="No"
      styles={{ root: { maxWidth: 300 } }}
      onConfirm={onConfirm}
    >
      <Button type="default" danger icon={<DeleteOutlined />}>
        Batch deletion
      </Button>
    </Popconfirm>
  );
};

export default BatchDeleteAction;
