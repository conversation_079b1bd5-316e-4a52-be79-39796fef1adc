import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';

const urlPrefix = '/api/customer/customer-call';

/**
 * Get Magento customer groups list
 *
 * GET /api/customer/customer-call
 */
export async function getCustomerCallListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.CustomerCall>>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/**
 * create a call log
 *
 * PUT /api/customer/customer-call */
export async function addCustomerCall(
  data?: API.CustomerCall | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.CustomerCall>>(`${urlPrefix}`, config).then(
    (res) => res.message,
  );
}

/**
 * Update call log.
 *
 *  POST /api/customer/customer-call/{di} */
export async function updateCustomerCall(
  id?: number,
  data?: API.CustomerCall | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.CustomerCall>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/customer/customer-call/{id} */
export async function deleteCustomerCall(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
