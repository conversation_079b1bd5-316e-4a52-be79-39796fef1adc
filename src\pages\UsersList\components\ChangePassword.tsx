import React from 'react';
import { ProFormText } from '@ant-design/pro-form';
export type FormValueType = Partial<API.UserListItem>;

export type ChangePasswordProps = {
  values: Partial<API.UserListItem>;
};

const ChangePassword: React.FC<ChangePasswordProps> = (props) => {
  return (
    <>
      <ProFormText.Password
        rules={[{ required: true }]}
        width="md"
        name="currentPassword"
        label="Current Password"
        required
      />
      <ProFormText.Password rules={[]} width="md" name="password" label="Password" required={false} />
      <ProFormText.Password
        rules={[
          ({ getFieldValue }) => ({
            validator(_: any, value: any, cb: any) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Confirm password does not match!'));
            },
          }),
        ]}
        width="md"
        name="confirmPassword"
        label="Confirm password"
        required={false}
      />
    </>
  );
};

export default ChangePassword;
