import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { UploadFile } from 'antd';
import { Button, Col, Dropdown, message, Popover, Row, Space, Spin, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import ProForm, { ProFormText, ModalForm } from '@ant-design/pro-form';
import { sendEmail } from '@/services/app/Email/email';
import Util, { nl2br } from '@/util';
import HtmlEditor from '@/components/HtmlEditor';
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { ProFormCheckbox, ProFormTextArea } from '@ant-design/pro-components';
import CustomerContactSelectModal from '@/pages/Customer/Customer/components/CustomerContactSelectModal';
import { useModel } from '@umijs/max';
import { DictCode } from '@/constants';
import useUserOptions from '@/hooks/BasicData/useUserOptions';
import useEmailTemplate from '../../EmailTemplateList/hooks/useEmailTemplate';

const handleSend = async (fields: FormValueType) => {
  const hide = message.loading('Sending...', 0);

  const data = new FormData();
  if (fields.id) {
    data.append('id', `${fields.id}`);
  }
  data.append('receiver', `${fields.receiver || ''}`);
  if (fields.bcc) {
    data.append('bcc', `${fields.bcc}`);
  }
  data.append('sender', `${fields.sender}`);
  data.append('subject', `${fields.subject || ''}`);
  data.append('text_html', `${fields.text_html}`);
  if (fields.customer_id) {
    data.append('customer_id', `${fields.customer_id}`);
  }
  if (fields.internal) {
    data.append('internal', `${fields.internal}`);
  }
  if (fields.email_template_id) {
    data.append('email_template_id', `${fields.email_template_id}`);
  }

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      data.append(`files[${ind}]`, file?.originFileObj ?? '');
    });
  }

  try {
    await sendEmail(data);
    hide();
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Email> & { files?: UploadFile[]; internal?: boolean };

export type CreateCustomerEmailFormProps = {
  initialValues?: Partial<API.Email>;
  customer?: Partial<API.Customer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  htmlEditorId?: string;
  internal?: boolean; // Is this an internal email?
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formValues?: FormValueType) => void;
};

const CreateCustomerEmailForm: React.FC<CreateCustomerEmailFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, htmlEditorId, customer, internal } =
    props;

  const { initialState } = useModel('@@initialState');
  const { getDictByCode } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();

  // Customer selection modal
  const [openCustomerSelectionModal, setOpenCustomerSelectionModal] = useState(false);

  // show/hide BCC
  const [showBcc, setShowBcc] = useState<boolean>(false);

  const defaultEmailFrom = useMemo(() => getDictByCode(DictCode.EMAIL_FROM), [getDictByCode]);

  const { userOptions } = useUserOptions();

  const [keep, setKeep] = useState(false);

  // template selection
  const {
    template,
    setTemplateId,
    emailTemplateSelector,
    loading: loadingTemplate,
  } = useEmailTemplate();

  useEffect(() => {
    if (modalVisible && formRef && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(initialValues || {}) };

      if (!newValues.sender) {
        if (internal) {
          newValues.sender = Util.emailBuildSender(
            initialState?.currentUser?.email,
            initialState?.currentUser?.name,
          );
        } else {
          newValues.sender = defaultEmailFrom;
        }
      }

      let textHtml = '';
      if (internal) {
        if (customer?.name) {
          if (!newValues.subject) {
            newValues.subject = `Internal Message to ${customer?.name}`;
          }
        }

        if (customer?.name) {
          textHtml += '<br /><br /><hr />';
        }
        if (customer?.name) {
          textHtml += 'Customer: ' + customer.name + '<br />';
        }

        const contacts = customer?.contacts;
        if (contacts) {
          textHtml +=
            'Email: ' +
            contacts?.map((x) => `${x.fullname} &lt;${x.email}&gt;`)?.join(',') +
            '<br />';
        }

        if (customer?.created_on) {
          textHtml += 'Created At: ' + Util.dtToDMYHHMM(customer?.created_on) + '<br />';
        }

        if (customer?.address) {
          let tableHtml = '<table border="0"><tbody>';

          tableHtml += '<tr>';
          tableHtml += '<td>Address ' + '' + '</td>';
          tableHtml += '<td>' + customer?.address?.telephone + '</td>';
          tableHtml += '<td>' + customer?.address?.company + '</td>';
          tableHtml += '<td>' + customer?.address?.street + '</td>';
          tableHtml += '<td>' + customer?.address?.city + '</td>';
          tableHtml += '<td>' + customer?.address?.postcode + '</td>';
          tableHtml += '<td>' + (customer?.address?.region ?? '') + '</td>';
          tableHtml += '<td>' + customer?.address?.country?.name + '</td>';
          tableHtml += '</tr>';

          tableHtml += '</tbody></table>';
          textHtml += tableHtml + '<br />';
        }
      }

      // We replace text_plain by <br />
      // console.log(initialValues?.text_html);
      textHtml +=
        (initialValues?.text_html ? initialValues?.text_html : nl2br(initialValues?.text_plain)) ??
        '';
      newValues.text_html = textHtml;

      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [
    defaultEmailFrom,
    initialState?.currentUser?.email,
    initialState?.currentUser?.name,
    initialValues,
    internal,
    modalVisible,
    customer?.address,
    customer?.contacts,
    customer?.created_on,
    customer?.name,
  ]);

  useEffect(() => {
    if (modalVisible) {
      if (template) {
        formRef.current?.setFieldsValue({
          subject: template.subject,
          text_html: template.text_html || '',
        });
      }
    }
  }, [template, modalVisible]);

  return (
    <ModalForm<API.Email>
      title={
        <Row>
          <Col flex="auto">
            <Space size={32} style={{ width: '100%' }}>
              <span>New Email</span>
              {customer ? (
                <Space size={8}>
                  <span>Customer: </span>
                  <span>{customer.name}</span>
                  <Typography.Text copyable>{customer.contacts?.[0]?.email}</Typography.Text>
                </Space>
              ) : null}
            </Space>
          </Col>
          {internal ? null : (
            <Col flex="400px">
              <Space size={12}>
                {emailTemplateSelector}
                <ProFormCheckbox
                  initialValue={false}
                  label="Keep?"
                  name="keep"
                  tooltip="Keep the selected template in next time."
                  colon={false}
                  fieldProps={{
                    checked: keep,
                    onChange(e) {
                      setKeep(e.target.checked);
                    },
                  }}
                />
              </Space>
            </Col>
          )}
        </Row>
      }
      width="1000px"
      open={modalVisible}
      onOpenChange={(open) => {
        if (!keep) {
          setTemplateId(undefined);
        }
        handleModalVisible(open);
      }}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 4 }}
      labelWrap={true}
      wrapperCol={{ span: 20 }}
      formRef={formRef}
      colon={false}
      onFinish={async (value) => {
        const success = await handleSend({
          ...value,
          id: initialValues?.id,
          internal,
          customer_id: customer?.id,
          email_template_id: template?.id,
        });

        if (success) {
          handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
      submitter={{ searchConfig: { submitText: 'Send' } }}
    >
      <Spin spinning={loadingTemplate}>
        <ProFormText
          name="sender"
          label="From"
          placeholder={'Sender Email'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          addonAfter={
            <Space>
              <Button
                onClick={() => {
                  formRef.current?.setFieldValue(
                    'sender',
                    `${initialState?.currentUser?.name} <${initialState?.currentUser?.email}>`,
                  );
                }}
              >
                Me
              </Button>
              <Button
                onClick={() => {
                  formRef.current?.setFieldValue('sender', defaultEmailFrom);
                }}
              >
                Default
              </Button>
            </Space>
          }
        />
        <ProFormTextArea
          name="receiver"
          label={
            <Space direction="horizontal" size={6}>
              <div>To</div>
              <Space direction="horizontal">
                {!!customer?.contacts?.length && (
                  <Dropdown
                    menu={{
                      onClick: (info) => {
                        const selectedUser = customer?.contacts?.find(
                          (x) => `${x.email}` == info.key,
                        );
                        if (selectedUser) {
                          const oldReceiverTmp = formRef.current?.getFieldValue('receiver');
                          const oldReceiverListKv: any = Util.emailParseStrListKv(
                            oldReceiverTmp ?? '',
                          );

                          if (!oldReceiverListKv[`${selectedUser.email}`]) {
                            oldReceiverListKv[`${selectedUser.email}`] = [
                              selectedUser.email,
                              `${selectedUser.fullname}`,
                            ];
                          }

                          // flatten receivers list
                          const tmp = [];
                          // eslint-disable-next-line guard-for-in
                          for (const email in oldReceiverListKv) {
                            let receiver = email;
                            if (oldReceiverListKv[email]?.[1]) {
                              receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
                            }
                            tmp.push(receiver);
                          }

                          formRef.current?.setFieldValue('receiver', tmp.join(','));
                        }
                      },
                      items: customer?.contacts?.map((user) => ({
                        ...user,
                        key: user.email,
                        label: `${user.fullname} <${user.email}>`,
                      })) as any,
                    }}
                  >
                    <Button type="primary">
                      <Space>
                        C <DownOutlined />
                      </Space>
                    </Button>
                  </Dropdown>
                )}
                <Button
                  variant="solid"
                  color="blue"
                  onClick={() => {
                    setOpenCustomerSelectionModal(true);
                  }}
                >
                  A
                </Button>
                <Dropdown
                  menu={{
                    onClick: (info) => {
                      const selectedUser = (userOptions as API.CurrentUser[]).find(
                        (x) => `${x.user_id}` == info.key,
                      );
                      if (selectedUser) {
                        const oldReceiverTmp = formRef.current?.getFieldValue('receiver');
                        const oldReceiverListKv: any = Util.emailParseStrListKv(
                          oldReceiverTmp ?? '',
                        );

                        if (!oldReceiverListKv[`${selectedUser.email}`]) {
                          oldReceiverListKv[`${selectedUser.email}`] = [
                            selectedUser.email,
                            `${selectedUser.name}`,
                          ];
                        }

                        // flatten receivers list
                        const tmp = [];
                        // eslint-disable-next-line guard-for-in
                        for (const email in oldReceiverListKv) {
                          let receiver = email;
                          if (oldReceiverListKv[email]?.[1]) {
                            receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
                          }
                          tmp.push(receiver);
                        }

                        formRef.current?.setFieldValue('receiver', tmp.join(','));
                      }
                    },
                    items: (userOptions as API.CurrentUser[])?.map((user) => ({
                      ...user,
                      key: user.user_id,
                      label: `${user.initials} | ${user.name}`,
                    })) as any,
                  }}
                >
                  <Button>
                    <Space>S</Space>
                  </Button>
                </Dropdown>
              </Space>
            </Space>
          }
          placeholder={
            'To Emails: e.g. Aslam Doctor <<EMAIL>>,Julia <julia.hotmail.com>,<EMAIL>'
          }
          rules={[
            {
              required: true,
              message: 'Type or select emails',
            },
          ]}
          fieldProps={{ rows: 2 }}
          width={740}
          allowClear
          addonAfter={
            showBcc ? (
              <></>
            ) : (
              <Button
                type="link"
                onClick={() => {
                  setShowBcc(true);
                }}
              >
                BCC
              </Button>
            )
          }
        />
        {showBcc ? (
          <ProFormTextArea
            name="bcc"
            label={
              <Space direction="horizontal" size={6}>
                <div>BCC</div>
                <Space direction="vertical">
                  <Dropdown
                    menu={{
                      onClick: (info) => {
                        const selectedUser = (userOptions as API.CurrentUser[]).find(
                          (x) => `${x.user_id}` == info.key,
                        );
                        if (selectedUser) {
                          const oldReceiverTmp = formRef.current?.getFieldValue('bcc');
                          const oldReceiverListKv: any = Util.emailParseStrListKv(
                            oldReceiverTmp ?? '',
                          );

                          if (!oldReceiverListKv[`${selectedUser.email}`]) {
                            oldReceiverListKv[`${selectedUser.email}`] = [
                              selectedUser.email,
                              `${selectedUser.name}`,
                            ];
                          }

                          // flatten receivers list
                          const tmp = [];
                          // eslint-disable-next-line guard-for-in
                          for (const email in oldReceiverListKv) {
                            let receiver = email;
                            if (oldReceiverListKv[email]?.[1]) {
                              receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
                            }
                            tmp.push(receiver);
                          }

                          formRef.current?.setFieldValue('bcc', tmp.join(','));
                        }
                      },
                      items: (userOptions as API.CurrentUser[])?.map((user) => ({
                        ...user,
                        key: user.user_id,
                        label: `${user.initials} | ${user.name}`,
                      })) as any,
                    }}
                  >
                    <Button>
                      <Space>
                        Staff
                        <DownOutlined />
                      </Space>
                    </Button>
                  </Dropdown>
                </Space>
              </Space>
            }
            placeholder={
              'BCC Emails: e.g. Aslam Doctor <<EMAIL>>,Julia <julia.hotmail.com>,<EMAIL>'
            }
            fieldProps={{ rows: 1 }}
            width={740}
            allowClear
            addonAfter={
              <Space>
                <Button
                  type="link"
                  onClick={() => {
                    setShowBcc(false);
                  }}
                >
                  Hide
                </Button>
              </Space>
            }
          />
        ) : null}
        <ProFormText name="subject" label="Subject" />
        <ProFormUploadButton
          name="files"
          label="Attachments"
          title="Select Files"
          listType="text"
          formItemProps={{ wrapperCol: { span: 12 } }}
          fieldProps={{
            multiple: true,
            beforeUpload: (file) => {
              return false;
            },
            // onPreview: handlePreview,
            style: { marginBottom: 24 },
          }}
        />
        <ProForm.Item
          name={'text_html'}
          label={
            <>
              Body&nbsp;
              <Popover
                title="Usable keywords"
                content={
                  <>
                    <Typography.Text copyable={{ text: '%salutation%' }}>
                      <b>%salutation%</b>
                      {` e.g. Dear {firstName},`}
                    </Typography.Text>
                  </>
                }
              >
                <InfoCircleOutlined />
              </Popover>
            </>
          }
          style={{ width: '100%' }}
          labelCol={undefined}
          wrapperCol={{ span: 24 }}
        >
          <HtmlEditor
            id={htmlEditorId ?? `email_body_create`}
            initialFocus
            enableTextModule
            hideMenuBar
            height={400}
          />
        </ProForm.Item>

        <CustomerContactSelectModal
          modalVisible={openCustomerSelectionModal}
          handleModalVisible={setOpenCustomerSelectionModal}
          addCustomerContacts={(contacts) => {
            const oldReceiverTmp = formRef.current?.getFieldValue('receiver');
            const oldReceiverListKv: any = Util.emailParseStrListKv(oldReceiverTmp ?? '');
            contacts.forEach((contact) => {
              if (!oldReceiverListKv[`${contact.email}`]) {
                oldReceiverListKv[`${contact.email}`] = [contact.email, contact.fullname];
              }
            });

            // flatten receivers list
            const tmp = [];
            // eslint-disable-next-line guard-for-in
            for (const email in oldReceiverListKv) {
              let receiver = email;
              if (oldReceiverListKv[email]?.[1]) {
                receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
              }
              tmp.push(receiver);
            }

            formRef.current?.setFieldValue('receiver', tmp.join(','));
          }}
        />
      </Spin>
    </ModalForm>
  );
};

export default CreateCustomerEmailForm;
