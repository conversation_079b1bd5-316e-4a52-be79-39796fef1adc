import type { FooterToolbarProps } from '@ant-design/pro-layout/lib/components/FooterToolbar';
import FooterToolbar from '@ant-design/pro-layout/lib/components/FooterToolbar';
import type { ActionType } from '@ant-design/pro-table';
import { Space } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import BatchDeleteAction from './BatchDeleteAction';

const SFooterToolbar: React.FC<
  FooterToolbarProps & {
    title: string;
    selectedRowsState: any[];
    setSelectedRows: Dispatch<SetStateAction<any[]>>;
    actionRef: React.MutableRefObject<ActionType | undefined>;
    handleRemove?: any;
  }
> = ({ selectedRowsState, setSelectedRows, actionRef, title, handleRemove, ...rest }) => {
  return selectedRowsState?.length > 0 ? (
    <FooterToolbar
      {...rest}
      extra={
        <Space>
          <span>
            Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
            &nbsp;{title ?? 'row'}s.
          </span>
          <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
        </Space>
      }
    >
      <BatchDeleteAction
        title={title}
        onConfirm={async () => {
          await handleRemove(selectedRowsState);
          setSelectedRows([]);
          actionRef.current?.reloadAndRest?.();
        }}
      />
    </FooterToolbar>
  ) : (
    <></>
  );
};
export default SFooterToolbar;
