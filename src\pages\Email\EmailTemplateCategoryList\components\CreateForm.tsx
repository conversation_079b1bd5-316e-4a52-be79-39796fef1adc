import type { Dispatch, SetStateAction } from 'react';
import React, { useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailTemplateCategory } from '@/services/app/Email/email-template-category';
import { AutoCompleteProps, message } from 'antd';
import Util from '@/util';
import { ProFormCheckbox, ProFormDigit } from '@ant-design/pro-components';

const handleAdd = async (fields: API.EmailTemplateCategory) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailTemplateCategory(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailTemplateCategory>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplateCategory) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  // const [options, setOptions] = useState<AutoCompleteProps['options']>([]);

  return (
    <ModalForm
      title={'New Email Template Category'}
      width="600px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailTemplateCategory);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        width="lg"
        name="cat1"
        label="Category Name"
        rules={[
          {
            required: true,
            message: 'Category Name is required',
          },
        ]}
        required
      />

      <ProFormDigit width="xs" name="sort" label="Sort" />

      <ProFormCheckbox name="gen_sort" label="Generate a sort No?" initialValue={1} />

      {/* <ProFormItem
        name="cat1"
        label="Category Name"
        help="Type something to find existed one or to create a new one"
        tooltip="Type something to find existed one or to create a new one"
      >
        <AutoComplete
          allowClear
          options={options}
          onSearch={(value) => {
            getCat1ACList({ keyWords: value }).then((res) => {
              setOptions(res as unknown as DefaultOptionType[]);
            });
          }}
          style={{ width: 400 }}
        />
      </ProFormItem> */}
    </ModalForm>
  );
};

export default CreateForm;
