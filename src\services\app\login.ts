import { request } from 'umi';
import { currentUser as queryCurrentUser } from '@/services/app/api';
import { LS_TOKEN_NAME } from '@/constants';

/** Login POST /api/login/outLogin */
export async function outLogin() {
  localStorage.setItem(LS_TOKEN_NAME, '');
  return queryCurrentUser({
    skipErrorHandler: true,
  }).catch(() => { });
  /*return request<Record<string, any>>('/api/user/logout', {
    method: 'POST',
    ...(options || {}),
  });*/
}

/** account POST /api/login/account */
export async function login(body: API.LoginParams, options?: { [key: string]: any }): Promise<API.LoginResult> {
  return request<API.BaseResult>('/api/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...options,
    skipToken: true,
  }).then((res) => res.message);
}
