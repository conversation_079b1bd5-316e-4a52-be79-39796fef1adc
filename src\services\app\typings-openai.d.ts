declare namespace API {
  type OpenapiChoice = {
    text?: string;
    message?: {
      role?: string;
      content?: string;
    };
    index?: number;
    logprobs?: any;
    finish_reason?: any;
  };

  type OpenapiUsage = {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };

  type OpenapiCallLog = {
    id?: number;
    model?: string;
    prompt?: string;
    max_tokens?: number;
    temperature?: number;

    res_id?: string;
    object?: string;
    choice?: OpenapiChoice;
    choices?: OpenapiChoice[];

    created?: number;
    usage?: OpenapiUsage;
    updated_on?: string;
  } & CreatorData & {
      answers?: Record<string, string>;
    } & {
      children?: OpenapiCallLog[];
    } & {
      prompt_title?: string;
    };
}
