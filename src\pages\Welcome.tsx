import { testDB } from '@/services/app/api';
import { getSysLogList } from '@/services/app/Sys/sys-log';
import Util from '@/util';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Card, Col, message, Row, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

const Welcome: React.FC = () => {
  // const { token } = theme.useToken();
  const { initialState } = useModel('@@initialState');
  const { loadAppSetting, appSettings } = useModel('app-settings');

  useEffect(() => {
    loadAppSetting();
  }, [loadAppSetting]);

  const columnsSysLog: ProColumns<API.SysLog>[] = [
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => (
        <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 250,
      ellipsis: true,
    },

    {
      title: 'Note',
      dataIndex: 'note',
      ellipsis: true,
    },
    {
      title: 'Date',
      dataIndex: 'created_on',
      width: 110,
      render: (dom, record) =>
        record.created_on ? (
          <div title={Util.dtToDMYHHMM(record.created_on)}>
            {Util.dtToDMYHHMM(record.created_on)}
          </div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 120,
    },
  ];

  return (
    <PageContainer
      extra={`Server TimeZone: ${appSettings.serverTz}, browser: ${Util.dtBrowserTzOffset()}`}
    >
      <Row gutter={24}>
        <Col xs={24} lg={24} xl={10} xxl={10} style={{ marginTop: 24 }}>
          <Card styles={{ body: { padding: 0 } }}>
            <ProTable<API.SysLog, API.PageParams>
              rowKey="id"
              size="small"
              headerTitle="System Log"
              revalidateOnFocus={false}
              options={{ reload: true, density: false, setting: false }}
              search={false}
              params={{ action_type: 0, with: 'user' }}
              // request={getSysLogList}
              request={(params, sort, filter) => getSysLogList(params, { id: 'descend' })}
              columns={columnsSysLog}
              pagination={false}
              columnEmptyText=""
            />
            <Typography.Link
              href="/monitor/sys-log"
              style={{ display: 'block', float: 'right', marginBottom: 10, marginRight: 32 }}
            >
              {' '}
              More...
            </Typography.Link>
          </Card>
        </Col>
        <Col xs={24} lg={24} xl={10} xxl={10} style={{ marginTop: 24 }}>
          <Button
            onClick={() => {
              const hide = message.loading('Checking Supplier....');
              testDB({ mode: 'supplier' })
                .catch(Util.error)
                .then((res) => {
                  message.success('Success ' + res.supplier_cnt);
                })
                .finally(hide);
            }}
          >
            Supp DB
          </Button>
          <Button
            onClick={() => {
              const hide = message.loading('Checking Org....');
              testDB({ mode: 'org' })
                .catch(Util.error)
                .then((res) => {
                  message.success('Success ' + res.offer_cnt);
                })
                .finally(hide);
            }}
          >
            Org DB
          </Button>
          <Button
            onClick={() => {
              const hide = message.loading('Checking Task....');
              testDB({ mode: 'task' })
                .catch(Util.error)
                .then((res) => {
                  message.success('Success ' + res.task_cnt);
                })
                .finally(hide);
            }}
          >
            Task DB
          </Button>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Welcome;
