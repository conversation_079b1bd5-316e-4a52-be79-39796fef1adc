import React from 'react';
import { <PERSON>, Col, Divider, Row, Typography } from 'antd';
import { FileOutlined } from '@ant-design/icons';
import styles from './OfferTemplateLangViewPartial.less';

const { Title, Text } = Typography;

export type OfferTemplateLangViewPartialProps = {
  values?: any;
};

const OfferTemplateLangViewPartial: React.FC<OfferTemplateLangViewPartialProps> = (props) => {
  const { values } = props;

  // Extract language data
  const deData = values?.DE || {};
  const enData = values?.EN || {};
  const files = values?.files || [];

  return (
    <>
      {/* Subject Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Subject DE</Title>
            <div className={styles.contentBox}>
              <Text>{deData.subject || '-'}</Text>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Subject EN</Title>
            <div className={styles.contentBox}>
              <Text>{enData.subject || '-'}</Text>
            </div>
          </div>
        </Col>
      </Row>

      {/* Header/Salutation Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Salutation DE</Title>
            <div className={styles.contentBox}>
              {deData.header ? (
                <div dangerouslySetInnerHTML={{ __html: deData.header }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Salutation EN</Title>
            <div className={styles.contentBox}>
              {enData.header ? (
                <div dangerouslySetInnerHTML={{ __html: enData.header }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
      </Row>

      {/* Body Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Body DE</Title>
            <div className={styles.contentBox} style={{ minHeight: 300 }}>
              {deData.body ? (
                <div dangerouslySetInnerHTML={{ __html: deData.body }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Body EN</Title>
            <div className={styles.contentBox} style={{ minHeight: 300 }}>
              {enData.body ? (
                <div dangerouslySetInnerHTML={{ __html: enData.body }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
      </Row>

      {/* Files Section - Positioned exactly like in the form */}
      <div className={styles.section}>
        <Title level={5}>Files</Title>
        <div className={styles.filesContainer}>
          {files.length > 0 ? (
            files.map((file: any, index: number) => (
              <Card
                key={file.id || index}
                size="small"
                className={styles.fileCard}
                hoverable
                onClick={() => window.open(file.url, '_blank')}
              >
                <div className={styles.fileItem}>
                  <FileOutlined className={styles.fileIcon} />
                  <Text ellipsis title={file.file_name || file.name}>
                    {file.file_name || file.name}
                  </Text>
                </div>
              </Card>
            ))
          ) : (
            <Text type="secondary">No files attached</Text>
          )}
        </div>
      </div>

      {/* Footer Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Footer DE</Title>
            <div className={styles.contentBox}>
              {deData.footer ? (
                <div dangerouslySetInnerHTML={{ __html: deData.footer }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Footer EN</Title>
            <div className={styles.contentBox}>
              {enData.footer ? (
                <div dangerouslySetInnerHTML={{ __html: enData.footer }} />
              ) : (
                <Text type="secondary">No content</Text>
              )}
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
};

export default OfferTemplateLangViewPartial;
