import { addProduct, getProductACList } from '@/services/app/Sys/product';
import Util from '@/util';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProFormInstance,
  ProFormSelect,
  ProFormSelectProps,
  RequestOptionsType,
} from '@ant-design/pro-components';
import { Button, Col, Divider, Input, InputRef, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

export const SProFormSelectProduct: React.FC<
  ProFormSelectProps & { formRef?: React.MutableRefObject<ProFormInstance | undefined> }
> = ({ name, label, mode, formRef, ...props }) => {
  const inputRef = useRef<InputRef>(null);
  const [newName, setNewName] = useState('');
  const [items, setItems] = useState<RequestOptionsType[]>([]);

  const [loadingNew, setLoadingNew] = useState(false);

  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();

    if (!newName) {
      return;
    }

    setLoadingNew(true);
    addProduct({ name: newName })
      .then((res) => {
        setNewName('');
        setItems([{ ...res, value: res.id, label: res.name }, ...items]);
        const oldValues: any[] = formRef?.current?.getFieldValue(name);

        const found = oldValues?.findIndex((x: any) => x.value == res.id) >= 0;

        if (!found) {
          formRef?.current?.setFieldValue(name, [
            { ...res, value: res.id, label: res.name },
            ...oldValues,
          ]);
        }

        setTimeout(() => {
          inputRef.current?.focus();
        }, 0);
      })
      .catch(Util.error)
      .finally(() => setLoadingNew(false));
  };

  useEffect(() => {
    getProductACList({}).then((res) => setItems(res));
  }, []);

  return (
    <ProFormSelect
      {...props}
      name={name}
      label={label}
      mode={mode}
      options={items as any}
      showSearch
      fieldProps={{
        ...props.fieldProps,
        labelInValue: true,
        notFoundContent: 'Not found',
        virtual: true,
        onSearch(value) {
          getProductACList({ keyWords: value }).then((res) => setItems(res));
        },
        dropdownRender(menu) {
          return (
            <>
              {menu}
              <Divider style={{ margin: '8px 0' }} />
              <Row wrap={false} gutter={8}>
                <Col flex="auto">
                  <Input
                    placeholder="Please enter product"
                    ref={inputRef}
                    value={newName}
                    onChange={(e) => {
                      setNewName(e.target.value);
                    }}
                    onKeyDown={(e) => {
                      e.stopPropagation();
                      if (Util.isEnterKey(e)) {
                        addItem(e as any);
                      }
                    }}
                    readOnly={loadingNew}
                  />
                </Col>
                <Col flex="70px">
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={addItem}
                    loading={loadingNew}
                  >
                    Add
                  </Button>
                </Col>
              </Row>
            </>
          );
        },
      }}
    />
  );
};
