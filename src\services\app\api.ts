import qs from 'qs';
import { request } from 'umi';

export const paramsSerializer = (paramsTmp: any) => {
  return qs.stringify(paramsTmp, {
    encoder(str, defaultEncoder, charset, type) {
      if (type === 'value' && typeof str === 'boolean') return str ? 1 : '';
      return str;
    },
  });
};

/** user info GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/currentUser', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** user info GET /api/countries */
export async function getCountries(options?: { [key: string]: any }): Promise<any[]> {
  return request<API.BaseResult>('/api/countries', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

const urlPrefixMagento = '/api/magento';

/** store websites info GET /api/app-settings */
export async function getAppSettings(options?: { [key: string]: any }): Promise<API.AppSettings> {
  return request<API.BaseResult>('/api/app-settings', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** 
 * Download file
 *  GET /api/download */
export async function downloadFileB64(params?: { b64: boolean, type: string, key: string }) {
  return request<API.ResultObject<{ b64: string }>>('/api/download', {
    method: 'GET',
    params,
  }).then((res) => res.message?.b64);
}


/** 
 * Download file
 *  GET /status */
export async function testDB(params?: { mode: 'supplier' | 'org' | 'task' }) {
  return request<API.ResultObject<any>>('/status', {
    method: 'GET',
    params,
  }).then((res) => res.message);
}
