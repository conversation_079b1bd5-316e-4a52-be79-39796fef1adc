import { FileOutlined, PaperClipOutlined } from '@ant-design/icons';
import { Avatar, Modal, Space, Image, Typography } from 'antd';
import { useState } from 'react';

type AttachmentIconIconProps = {
  id?: number;
  attachments?: API.EmailAttachment[];
};

const AttachmentIconIcon: React.FC<AttachmentIconIconProps> = ({ id, attachments, ...rest }) => {
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState<boolean>(false);
  return attachments?.length ? (
    <>
      <PaperClipOutlined
        className="c-grey cursor-pointer"
        title={`${attachments.length} attachments`}
        onClick={() => setOpenAttachmentsModal(true)}
      />
      <Modal
        open={openAttachmentsModal}
        onCancel={() => setOpenAttachmentsModal(false)}
        width={500}
        title="Email Attachments"
        footer={false}
      >
        <Space size={24}>
          {attachments.map((a) => {
            const fileUrl = `${API_URL}/api/download?key=${a.file_path}`;
            return (
              <Space key={a.name} direction="vertical">
                {a.mime_type?.includes('image') ? (
                  <Avatar shape="square" size={128} src={<Image src={fileUrl} />} />
                ) : (
                  <FileOutlined style={{ fontSize: 128, color: 'grey' }} />
                )}
                <Typography.Link
                  href={fileUrl}
                  ellipsis
                  target="_blank"
                  className="text-sm"
                  style={{ textAlign: 'center', maxWidth: 128 }}
                >
                  {a.org_name}
                </Typography.Link>
              </Space>
            );
          })}
        </Space>
      </Modal>
    </>
  ) : null;
};

export default AttachmentIconIcon;
