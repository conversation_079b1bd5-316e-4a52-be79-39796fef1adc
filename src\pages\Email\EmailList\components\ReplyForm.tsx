import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { UploadFile } from 'antd';
import { message, Popover, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import <PERSON>Form, { ProFormText, ModalForm } from '@ant-design/pro-form';
import { sendEmail } from '@/services/app/Email/email';
import Util, { nl2br } from '@/util';
import HtmlEditor from '@/components/HtmlEditor';
import { InfoCircleOutlined } from '@ant-design/icons';

const handleReply = async (fields: FormValueType) => {
  const hide = message.loading('Replying...', 0);

  const data = new FormData();
  if (fields.id) {
    data.append('id', `${fields.id}`);
  }
  data.append('receiver', `${fields.receiver}`);
  data.append('sender', `${fields.sender}`);
  data.append('subject', `${fields.subject || ''}`);
  data.append('text_html', `${fields.text_html}`);
  if (fields.email_template_id) {
    data.append('email_template_id', `${fields.email_template_id}`);
  }

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      data.append(`files[${ind}]`, file?.originFileObj ?? '');
    });
  }

  try {
    await sendEmail(data);
    hide();
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Email> & { files?: UploadFile[] };

export type ReplyFormProps = {
  mode?: 'create' | 'reply'; // Default: 'reply'
  initialValues?: Partial<API.Email>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formValues?: FormValueType) => void;
};

const ReplyForm: React.FC<ReplyFormProps> = (props) => {
  const { mode, initialValues, modalVisible, handleModalVisible } = props;
  const formRef = useRef<ProFormInstance>();

  const [loadingSender, setLoadingSender] = useState(false);
  // const [senders, setSenders] = useState<ItemType[]>([]);

  useEffect(() => {
    if (modalVisible && formRef && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(initialValues || {}) };
      newValues.receiver = initialValues?.sender;
      if (mode != 'create') {
        newValues.sender = initialValues?.receiver;
      } else {
        newValues.sender = '';
      }
      newValues.subject = 'RE: ' + initialValues?.subject;

      // Bug here emails on email.de.
      // newValues.text_html = `<br /><hr style="border-bottom: 1px solid #ddd" /><blockquote>${initialValues?.text}</blockquote>`;

      // We replace text_plain by <br />
      const textHtml =
        (initialValues?.text_html ? initialValues?.text_html : nl2br(initialValues?.text_plain)) ??
        '';
      newValues.text_html = `<br /><hr style="border-bottom: 1px solid #ddd" /><blockquote>${textHtml}</blockquote>`;

      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, formRef, initialValues, mode]);

  const handlePreview = async (file: UploadFile) => {
    /* if (!file.url && !file.preview) {
      file.preview = await Util.getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1)); */
  };

  return (
    <ModalForm<API.Email>
      title={mode == 'create' ? 'Send Email' : 'Reply Email'}
      width="800px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 20 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleReply({ ...value, id: initialValues?.id });

        if (success) {
          handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        name="sender"
        label="From"
        rules={[
          {
            required: true,
          },
        ]}
        width="xl"
        /* addonAfter={
          <Dropdown
            menu={{
              onClick: (info) => {
                if (mode == 'create') {
                  const oldStr = formRef.current?.getFieldValue('sender');
                  formRef.current?.setFieldValue(
                    'sender',
                    oldStr ? oldStr + ',' + info.key : info.key,
                  );
                } else {
                  formRef.current?.setFieldValue('sender', info.key);
                }
              },
              items: senders,
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <DownOutlined />
            </a>
          </Dropdown>
        } */
      />
      <ProFormText
        name="receiver"
        label="To"
        rules={[
          {
            required: true,
          },
        ]}
      />
      <ProFormText name="subject" label="Subject" />
      <ProFormUploadButton
        name="files"
        label="Attachments"
        title="Select Files"
        listType="text"
        formItemProps={{ wrapperCol: { span: 12 } }}
        fieldProps={{
          multiple: true,
          beforeUpload: (file) => {
            return false;
          },
          onPreview: handlePreview,
          style: { marginBottom: 24 },
        }}
      />
      <ProForm.Item
        name={'text_html'}
        label={
          <>
            Body&nbsp;
            <Popover
              title="Usable keywords"
              content={
                <>
                  <Typography.Text copyable={{ text: '%salutation%' }}>
                    <b>%salutation%</b>
                    {` e.g. Dear {firstName},`}
                  </Typography.Text>
                </>
              }
            >
              <InfoCircleOutlined />
            </Popover>
          </>
        }
        style={{ width: '100%' }}
        labelCol={undefined}
        wrapperCol={{ span: 24 }}
      >
        <HtmlEditor
          id={`email_body_${mode ?? 'reply'}`}
          initialFocus
          enableTextModule
          hideMenuBar
          height={400}
        />
      </ProForm.Item>
    </ModalForm>
  );
};

export default ReplyForm;
