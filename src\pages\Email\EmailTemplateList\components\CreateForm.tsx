import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailTemplate } from '@/services/app/Email/email-template';
import { Col, message, Popover, Row, Typography } from 'antd';
import Util from '@/util';
import { ProForm, ProFormCheckbox, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import HtmlEditor from '@/components/HtmlEditor';
import { getEmailTemplateCatACList } from '@/services/app/Email/email-template-category';
import { InfoCircleOutlined } from '@ant-design/icons';

const handleAdd = async (fields: API.EmailTemplate) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailTemplate(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailTemplate>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplate) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Email Template'}
      width="1000px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      grid
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailTemplate);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <Col span={14}>
        <ProFormText
          width="lg"
          name="title"
          label="Template Title"
          rules={[
            {
              required: true,
              message: 'Title is required',
            },
          ]}
          required
        />

        <ProFormText width="lg" name="subject" label="Subject" />
      </Col>
      <Col span={8}>
        <ProFormSelect
          name="category_id"
          label="Category"
          request={(params) => {
            return getEmailTemplateCatACList(params);
          }}
          allowClear
          showSearch
        />

        <Row>
          <Col span={12}>
            <ProFormDigit width="xs" name="sort" label="Sort" />
          </Col>
          <Col span={12}>
            <ProFormCheckbox name="gen_sort" label="Generate a sort No?" initialValue={1} />
          </Col>
        </Row>
      </Col>

      <ProForm.Item
        name={'text_html'}
        label={
          <>
            Template&nbsp;
            <Popover
              title="Usable keywords"
              content={
                <>
                  <Typography.Text copyable={{ text: '%salutation%' }}>
                    <b>%salutation%</b>
                    {` e.g. Dear {firstName},`}
                  </Typography.Text>
                </>
              }
            >
              <InfoCircleOutlined />
            </Popover>
          </>
        }
        style={{ width: '100%' }}
        labelCol={undefined}
        wrapperCol={{ span: 24 }}
      >
        <HtmlEditor
          id={`email_body_create`}
          initialFocus
          enableTextModule
          hideMenuBar
          toolbarMode={2}
          height={400}
        />
      </ProForm.Item>
    </ModalForm>
  );
};

export default CreateForm;
