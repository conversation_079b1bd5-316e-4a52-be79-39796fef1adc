import { EmailTrackingListItemType, getEmailTrackingList } from '@/services/app/Email/email';
import Util, { sn } from '@/util';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Modal, Typography } from 'antd';
import { Dispatch, SetStateAction, useEffect, useRef } from 'react';

type RowType = EmailTrackingListItemType;

export type EmailTrackingListModalProps = {
  emailId: number;
  emailAddr: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const EmailTrackingListModal: React.FC<EmailTrackingListModalProps> = (props) => {
  const { emailId, emailAddr, modalVisible, handleModalVisible } = props;

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Date',
      dataIndex: 'date',
      width: 120,
      render(dom, entity) {
        return Util.dtToDMYHHMM(entity.date);
      },
    },
    {
      title: 'Log',
      dataIndex: 'log',
      render(dom, entity) {
        if (entity.log == 'Sent') return dom;
        else {
          const logArr = entity.log.split(',');
          if (logArr.length > 1) {
            const ip = logArr[0].split(':')?.[1];
            return <>Seen. {ip ? <Typography.Text copyable>{ip}</Typography.Text> : null}</>;
          } else {
            return dom;
          }
        }
      },
    },
  ];

  useEffect(() => {
    if (modalVisible && emailId && emailAddr) {
      actionRef.current?.reload();
    }
  }, [modalVisible, emailId, emailAddr]);

  return (
    <Modal
      title={`Tracking History - ${emailAddr}`}
      width="500px"
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      footer={false}
    >
      <ProTable<RowType, API.PageParams>
        headerTitle={false}
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        cardProps={{ bodyStyle: { padding: 0 } }}
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        pagination={{
          defaultPageSize: 20,
          hideOnSinglePage: true,
        }}
        request={async (params, sort, filter) => {
          return getEmailTrackingList(emailId, { ...params, email: emailAddr }, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};
export default EmailTrackingListModal;
