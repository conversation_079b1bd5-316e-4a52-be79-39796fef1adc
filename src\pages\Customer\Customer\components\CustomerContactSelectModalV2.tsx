import Util from "@/util";
import { ActionType, ProColumns, ProForm, ProFormInstance, ProFormText, ProTable } from "@ant-design/pro-components";
import { <PERSON>ton, Card, Checkbox, Col, Modal, Popover, Row, Space } from "antd";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { DefaultContactIconComp } from "..";
import { InfoCircleOutlined } from "@ant-design/icons";
import { getCustomerListByPage } from "@/services/app/Customer/customer";
import CreateCustomerEmailForm from "@/pages/Email/EmailList/components/CreateCustomerEmailForm";
import CreateOfferEmailForm from "@/pages/Email/EmailList/components/CreateOfferEmailForm";

export type CustomerContactSelectModalV2SearchParamsType = {
  customerIds?: (number | undefined)[];
  offerNo?: string | number;
};

export type SearchFormValueType = Partial<API.CustomerContact> & API.PageParams;

type RowType = API.Customer;

type CustomerContactSelectModalV2Props = {
  searchParams?: CustomerContactSelectModalV2SearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const CustomerContactSelectModalV2: React.FC<CustomerContactSelectModalV2Props> = (props) => {
  const { searchParams, modalVisible, handleModalVisible } = props;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);

  const [receiver, setReceiver] = useState<string>("");
  const [openBulkSendMailModal, setOpenBulkSendMailModal] = useState<boolean>(false); // bulk Email
  const [selectedContacts, setSelectedContacts] = useState<Record<string, API.CustomerContact | null>>({});

  const [openBulkSendMailByOfferFormModal, setOpenBulkSendMailByOfferFormModal] = useState<boolean>(false); // bulk Email

  const initialLoaded = useRef(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Customer",
      dataIndex: ["name"],
      width: 250,
      showSorterTooltip: false,
      copyable: true,
      render(dom, entity) {
        return (
          <Row>
            <Col flex="auto">{dom}</Col>
            <Col flex="24px">{/* <DefaultContactIconComp is_default={entity.is_default} /> */}</Col>
          </Row>
        );
      },
    },
    {
      title: "Contacts",
      dataIndex: "contacts",
      width: 550,
      showSorterTooltip: false,
      copyable: false,
      className: "p-0",
      render(__, entity) {
        const contacts = entity.contacts || [];
        return (
          <Row>
            <Col flex="auto">
              {contacts.map((x) => {
                const styleDefaultColor = { style: { color: x.is_default ? "black" : "grey" } };
                return (
                  <Row key={x.id}>
                    <Col flex={"30px"}>
                      <Checkbox
                        checked={!!selectedContacts[`${x.id}`]}
                        onChange={(e) => {
                          setSelectedContacts((prev) => ({
                            ...prev,
                            [`${x.id}`]: e.target?.checked ? x : null,
                          }));
                        }}
                      />
                    </Col>
                    <Col flex="220px">{x.email}</Col>
                    <Col flex={"20px"}>
                      <DefaultContactIconComp is_default={x.is_default} />
                    </Col>
                    <Col flex="auto" {...styleDefaultColor}>
                      {x.fullname}
                    </Col>
                    <Col flex={"24px"}>
                      {!!x.note && (
                        <Popover content={x.note} styles={{ body: { maxWidth: 300 } }}>
                          <InfoCircleOutlined {...styleDefaultColor} />
                        </Popover>
                      )}
                    </Col>

                    <Col flex="130px" {...styleDefaultColor}>
                      {x.telephone}
                    </Col>
                  </Row>
                );
              })}
            </Col>
          </Row>
        );
      },
    },
  ];

  useEffect(() => {
    if (modalVisible && searchParams?.customerIds) {
      initialLoaded.current = false;
      actionRef.current?.reload();
    }
  }, [modalVisible, searchParams?.customerIds]);

  return (
    <Modal
      title={<>Customers Contacts List</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="900px"
      footer={false}
      styles={{ body: { paddingTop: 0 } }}
    >
      <Card variant="borderless">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: "Search" },
            submitButtonProps: { htmlType: "submit" },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
            render(props, dom) {
              return (
                <Row align="middle" style={{ flex: "1" }} gutter={8}>
                  <Col>{dom[0]}</Col>
                  <Col style={{ flex: "1" }}>{dom[1]}</Col>
                  <Col>
                    {!!searchParams?.offerNo && (
                      <Button
                        type="primary"
                        title="Send emails by Offer"
                        onClick={() => {
                          const rows = Object.values(selectedContacts)?.filter((x) => !!x);
                          if (rows.length) {
                            const str = rows.map((x) => Util.emailBuildSender(x.email, x.fullname))?.join(",");
                            setReceiver(str);
                            setOpenBulkSendMailByOfferFormModal(true);
                          }
                        }}
                      >
                        Send Emails
                      </Button>
                    )}

                    <Button
                      type="default"
                      title="Send emails by Template"
                      onClick={() => {
                        const rows = Object.values(selectedContacts)?.filter((x) => !!x);
                        if (rows.length) {
                          const str = rows.map((x) => Util.emailBuildSender(x.email, x.fullname))?.join(",");
                          setReceiver(str);
                          setOpenBulkSendMailModal(true);
                        }
                      }}
                      style={{ marginLeft: 8 }}
                    >
                      Send Emails by Template
                    </Button>
                  </Col>
                </Row>
              );
            },
          }}
        >
          <ProFormText name={"keyWords"} label="KeyWords" width={140} placeholder={"Email / Name / Contact Names"} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: 20,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("cu_sf_customer_contact_m", searchFormValues);
          Util.setSfValues("cu_sf_customer_contact_m_p", params);

          setLoading(true);
          return getCustomerListByPage(
            {
              ...searchFormValues,
              ...params,
              ids: searchParams?.customerIds || [],
              with: "contacts",
            },
            sort,
            filter,
          )
            .then((res) => {
              if (!initialLoaded.current) {
                const prev: any = {};
                res.data?.forEach((x) => {
                  x.contacts?.forEach((y) => {
                    prev[`${y.id}`] = y;
                  });
                });
                setSelectedContacts(prev);
                initialLoaded.current = true;
              }

              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />

      {!!searchParams?.offerNo && (
        <CreateOfferEmailForm
          offerNo={searchParams?.offerNo}
          customerContactIds={Object.values(selectedContacts)
            ?.filter((x) => !!x)
            ?.map((x) => x.id)}
          modalVisible={openBulkSendMailByOfferFormModal}
          handleModalVisible={setOpenBulkSendMailByOfferFormModal}
          initialValues={{ receiver: receiver }}
          htmlEditorId="bulk_email_by_offer"
        />
      )}

      <CreateCustomerEmailForm
        modalVisible={openBulkSendMailModal}
        handleModalVisible={setOpenBulkSendMailModal}
        initialValues={{ receiver: receiver }}
        htmlEditorId="bulk_email_by_template"
      />
    </Modal>
  );
};

export default CustomerContactSelectModalV2;
