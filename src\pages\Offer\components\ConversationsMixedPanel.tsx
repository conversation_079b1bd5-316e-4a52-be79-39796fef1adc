import { Col, Row } from "antd";

type ConversationsMixedPanelSearchParamsType = { tmp?: any };

type ConversationsMixedPanelProps = {
  searchParams?: ConversationsMixedPanelSearchParamsType;
};

const ConversationsMixedPanel: React.FC<ConversationsMixedPanelProps> = (props) => {
  return (
    <Row gutter={16}>
      <Col span={8}>
        <h3>Supplier:</h3>
      </Col>
      <Col span={8}>
        <h3>VK / EK:</h3>
      </Col>
      <Col span={8}>
        <h3>Customer:</h3>
      </Col>
    </Row>
  );
};

export default ConversationsMixedPanel;
