import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateProduct } from '@/services/app/Sys/product';
import Util, { sn } from '@/util';
import { ProFormText } from '@ant-design/pro-components';

export type FormValueType = Partial<API.Product>;

const handleUpdate = async (id: number, fields?: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateProduct(id, fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.Product>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Product) => Promise<boolean | void>;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { handleModalVisible, modalVisible, initialValues, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, initialValues]);

  return (
    <ModalForm
      title={'Update Product'}
      width="500px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate(sn(initialValues?.id), value);

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProFormText
        width="xl"
        name="name"
        label="Name"
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
      />
    </ModalForm>
  );
};

export default UpdateForm;
