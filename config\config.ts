// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import { join } from 'path';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV = 'dev' } = process.env;

/**
* @name Use public path
* @description The path when deploying. If deployed in a non-root directory, you need to configure this variable
* @doc https://umijs.org/docs/api/config#publicpath
*/
const PUBLIC_PATH: string = '/whc_supplier/';

export default defineConfig({
  /**
  * @name Enable hash mode
  * @description Make the product after build contain hash suffix. Usually used for incremental release and avoiding browser loading cache.
  * @doc https://umijs.org/docs/api/config#hash
  */
  hash: true,

  base: PUBLIC_PATH,
  publicPath: PUBLIC_PATH,
  // publicPath: 'https://*************/whc_supplier/', // production setting

  /**
* @name Compatibility settings
* @description Setting ie11 may not be perfectly compatible, you need to check all the dependencies you use
* @doc https://umijs.org/docs/api/config#targets
*/
  // targets: {
  //   ie: 11,
  // },
  /**
  * @name Routing configuration, files not introduced in routing will not be compiled
  * @description Only supports path, component, routes, redirect, wrappers, title configuration
  * @doc https://umijs.org/docs/guides/routes
  */
  // umi routes: https://umijs.org/docs/routing
  routes,
  /**
* @name Theme configuration
* @description Although it is called a theme, it is actually just a variable setting for less
* @doc antd theme settings https://ant.design/docs/react/customize-theme-cn
* @doc umi theme configuration https://umijs.org/docs/api/config#theme
*/
  theme: {
    // If you don't want configProvide to dynamically set the theme, you need to set this to default
    // Only if it is set to variable can configProvide be used to dynamically set the main color
    'root-entry-name': 'variable',
  },
  /**
* @name moment internationalization configuration
* @description If there is no requirement for internationalization, turning it on can reduce the size of the js package
* @doc https://umijs.org/docs/api/config#ignoremomentlocale
*/
  ignoreMomentLocale: true,
  /**
* @name Proxy configuration
* @description You can proxy your local server to your server so that you can access the server data
* @see Please note that the proxy can only be used during local development and cannot be used after build.
* @doc Proxy introduction https://umijs.org/docs/guides/proxy
* @doc Proxy configuration https://umijs.org/docs/api/config#proxy
*/
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  /**
   * @name Fast hot update configuration
   * @description A nice hot update component that can be retained during update state
   */
  fastRefresh: true,
  //============== The following are all max plug-in configurations ===============
  /**
   * @name Data Stream Plugin
   * @@doc https://umijs.org/docs/max/data-flow
   */
  model: {},
  /**
* A global initial data flow that can be used to share data between plugins
* @description It can be used to store some global data, such as user information, or some global status. 
*             The global initial status is created at the beginning of the entire Umi project.
* @doc https://umijs.org/docs/max/data-flow#%E5%85%A8%E5%B1%80%E5%88%9D%E5%A7%8B%E7%8A%B6%E6%80%81
*/
  initialState: {},
  /**
* @name layout plugin
* @doc https://umijs.org/docs/max/layout-menu
*/
  title: 'WHC Supplier',
  layout: {
    locale: false,
    ...defaultSettings,
    favicon: PUBLIC_PATH + 'favicon.ico',
  },
  /**
* @name moment2dayjs plugin
* @description Replace moment in the project with dayjs
* @doc https://umijs.org/docs/max/moment2dayjs
*/
  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration'],
  },
  /**
   * @name Internationalization plugin
   * @doc https://umijs.org/docs/max/i18n
   */
  locale: {
    default: 'en-US',
    antd: true,
    baseNavigator: false,
  },
  /**
* @name antd plugin
* @description Built-in babel import plugin
* @doc https://umijs.org/docs/max/antd#antd
*/
  antd: {
    theme: {
      components: {
        Card: {
          colorBorderSecondary: "#eeeeee"
        },
        Table: {
          borderColor: '#dcdcdc'
        }
      },
      token: {
        colorBorder: '#dcdcdc',
      },
    },
    compact: true,

  },
  /**
* @name Network request configuration
* @description It provides a unified network request and error handling solution based on useRequest of axios and ahooks.
* @doc https://umijs.org/docs/max/request
*/
  request: {},
  /**
* @name Permission plugin
* @description Permission plugin based on initialState, initialState must be turned on first
* @doc https://umijs.org/docs/max/access
*/
  access: {},
  /**
* @name Additional script in <head>
* @description Configure additional script in <head>
*/
  headScripts: [
    // Solve the problem of white screen when loading for the first time
    { src: join(PUBLIC_PATH, 'scripts/loading.js'), async: true },
  ],
  //================ pro Plugin Configuration =================
  presets: ['umi-presets-pro'],
  /**
  * @name Configuration of openAPI plugin
  * @description Generate serve and mock based on openapi specification, which can reduce a lot of boilerplate code
  * @doc https://pro.ant.design/zh-cn/docs/openapi/
  */
  /* openAPI: [
    {
      requestLibPath: "import { request } from '@umijs/max'",
      // Or use the online version
      // schemaPath: "https://gw.alipayobjects.com/os/antfincdn/M%24jrzTTYJN/oneapi.json"
      schemaPath: join(__dirname, 'oneapi.json'),
      mock: false,
    },
    {
      requestLibPath: "import { request } from '@umijs/max'",
      schemaPath: 'https://gw.alipayobjects.com/os/antfincdn/CA1dOm%2631B/openapi.json',
      projectName: 'swagger',
    },
  ], */
  /**
* @name Whether to enable mako
* @description Use mako for rapid development
* @doc https://umijs.org/docs/api/config#mako
*/
  mako: {},
  esbuildMinifyIIFE: true,
  requestRecord: {},
  define: {
    API_URL: 'https://*************:446',
    // API_URL: 'http://localhost/whc-supplier-backend/public',  // DEV TEST
    FSONE_PIM_URL: 'https://food.yuuru.net',
    PUBLIC_PATH: PUBLIC_PATH,
  },
  devtool: process.env.NODE_ENV === 'development' ? 'eval' : false,// disable source map in build (production)
});
