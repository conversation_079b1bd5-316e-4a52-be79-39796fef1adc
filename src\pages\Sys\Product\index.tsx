import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { deleteProduct, getProductList } from '@/services/app/Sys/product';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import CreateForm from './components/CreateForm';
import { Button, Card, message } from 'antd';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import { FooterToolbar } from '@ant-design/pro-components';
import UpdateForm from './components/UpdateForm';

const ProductList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<API.Product>>();

  const [currentRow, setCurrentRow] = useState<API.Product>();
  const [selectedRows, setSelectedRows] = useState<API.Supplier[]>([]);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.Product>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      width: 80,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      width: 300,
    },
    {
      title: '',
      valueType: 'option',
      render(__, record) {
        return (
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRow(record);
              handleUpdateModalVisible(true);
            }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_product_categories', {}));
  }, []);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.Product>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.setFieldsValue({});
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormText name="name" label="Name" width="sm" />
        </ProForm>
      </Card>

      <ProTable<API.Product, API.PageParams>
        headerTitle={'Products'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        size="small"
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_product_categories', searchFormValues);
          return getProductList({ ...params, ...searchFormValues }, sort, filter);
        }}
        columns={columns}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        rowSelection={{
          // selectedRowKeys: selectedRows?.map((x) => x.id as React.Key),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRows.length}
              </a>{' '}
              products &nbsp;&nbsp;
            </div>
          }
        >
          <BatchDeleteAction
            title="products"
            onConfirm={async () => {
              const hide = message.loading('Deleting...', 0);
              if (!selectedRows) return true;

              try {
                await deleteProduct(selectedRows.map((row) => row.id).join(','));
                hide();
                message.success('Deleted successfully and will refresh soon');
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              } catch (error) {
                hide();
                Util.error(error);
              }
            }}
          />
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default ProductList;
