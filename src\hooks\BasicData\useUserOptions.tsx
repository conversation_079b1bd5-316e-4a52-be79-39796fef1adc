import { getOfferACList } from '@/services/app/Offer/offer';
import { getUsersList } from '@/services/app/user';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of Active User
 */
export default (
  defaultParams?: Record<string, any>,
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: any,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [userOptions, setUserOptions] = useState<DefaultOptionType[]>([]);

  const searchUserOptions = useCallback(async (params?: Record<string, any>) => {
    setLoading(true);

    getUsersList({ status: 1, pageSize: 1000, ...params }, {}, {})
      .then((res) => {
        setUserOptions(
          res.data.map((x: API.CurrentUser) => ({
            ...x,
            value: x.user_id,
            label: `${x.initials ?? x.username}`,
          })),
        );
      })
      .catch(Util.error);
  }, []);

  useEffect(() => {
    searchUserOptions();
  }, [searchUserOptions]);

  return {
    userOptions,
    searchUserOptions,
    loading,
  };
};
