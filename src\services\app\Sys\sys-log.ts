/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import Util from '@/util';
import { SysLogCategory } from '@/constants';

const urlPrefix = '/api/sys/sys-log';

/** 
 * Get System Logs
 * GET /api/sys/sys-log 
 * */
export async function getSysLogList(
  params: API.PageParams & Partial<API.SysLog>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysLog>> {
  return request<API.Result<API.SysLog>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


/** 
 * create system log entry 
 * 
 * POST /api/sys/sys-log */
export async function addSysLog(
  category: SysLogCategory,
  name: string,
  note?: string,
  data?: Partial<API.SysLog>,
  options?: { [key: string]: any },
): Promise<boolean> {
  return request<API.ResultObject<boolean>>(`${urlPrefix}`, {
    method: 'POST',
    data: {
      category,
      name,
      note,
      ...data
    },
    ...(options || {}),
  }).then((res) => res.message).catch(err => { Util.error(err); return false });
}

/** 
 * delete System log
 * DELETE /api/sys/sys-log/{id} 
 * */
export async function deleteSysLog(codes?: string[], options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}`, {
    method: 'DELETE',
    data: {
      codes,
    },
    ...(options || {}),
  });
}

/**
 * Down syncing
 *
 * PUT GET /api/sys/sys-log/dsEmailTrackingData
 */
export async function dsEmailTrackingData(options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/dsEmailTrackingData`, {
    method: 'PUT',
    ...(options || {}),
  });
}