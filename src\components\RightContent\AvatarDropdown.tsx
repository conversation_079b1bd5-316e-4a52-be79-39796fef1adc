import { outLogin } from '@/services/app/login';
import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { message, Spin } from 'antd';
import type { MenuProps } from 'antd';
import { createStyles } from 'antd-style';
import { stringify } from 'querystring';
import React, { useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';
import { ModalForm, ProFormInstance } from '@ant-design/pro-components';
import { changeUserPassword } from '@/services/app/user';
import Util from '@/util';
import ChangePassword from '@/pages/UsersList/components/ChangePassword';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.name}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

/**
 *
 */
export const loginOut = async () => {
  await outLogin();
  const { search, pathname } = window.location;
  const urlParams = new URL(window.location.href).searchParams;

  const redirect = urlParams.get('redirect');
  // Note: There may be security issues, please note
  if (window.location.pathname !== '/user/login' && !redirect) {
    history.replace({
      pathname: '/user/login',
      search: stringify({
        redirect: pathname + search,
      }),
    });
  }
};

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  const { styles } = useStyles();

  const { initialState, setInitialState } = useModel('@@initialState');

  // Change password functionalities
  const updateFormRef = useRef<ProFormInstance>();
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const onMenuClick: MenuProps['onClick'] = (event) => {
    const { key } = event;
    if (key === 'logout') {
      flushSync(() => {
        setInitialState((s) => ({ ...s, currentUser: undefined }));
      });
      loginOut();
      return;
    } else if (key === 'change-password') {
      handleUpdateModalVisible(true);
    } else {
      history.push(`/account/${key}`);
    }
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
          {
            key: 'change-password',
            icon: <UserOutlined />,
            label: 'Change password',
          },
          /* {
            type: 'divider' as const,
          }, */
        ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
    },
  ];

  return (
    <>
      <HeaderDropdown
        menu={{
          selectedKeys: [],
          onClick: onMenuClick,
          items: menuItems,
        }}
      >
        {children}
      </HeaderDropdown>
      <ModalForm
        title={'Change password'}
        width="500px"
        open={updateModalVisible}
        onOpenChange={handleUpdateModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        initialValues={currentUser}
        formRef={updateFormRef}
        onFinish={async (value) => {
          if (!currentUser?.user_id) return;
          const hide = message.loading('Changing password...', 0);

          const fields = { ...value, user_id: currentUser?.user_id };
          try {
            await changeUserPassword(fields);
            message.success('Password has been changed successfully.');
            handleUpdateModalVisible(false);
          } catch (error) {
            Util.error(error);
          } finally {
            hide();
          }
        }}
      >
        <ChangePassword values={currentUser} />
      </ModalForm>
    </>
  );
};
