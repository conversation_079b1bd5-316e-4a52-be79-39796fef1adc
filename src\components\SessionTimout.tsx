import { LS_TOKEN_NAME } from '@/constants';
import { sn } from '@/util';
import { Alert } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';
import { loginOut } from './RightContent/AvatarDropdown';
import dayjs from 'dayjs';

const SessionTimeout: React.FC = () => {
  const tmp = localStorage.getItem(LS_TOKEN_NAME)
    ? JSON.parse(atob((localStorage.getItem(LS_TOKEN_NAME) || '').split('.')[1]))
    : null;
  const remainingSeconds = (sn(tmp?.exp) * 1000 - Date.now()) / 1000;
  // to do
  // const remainingSeconds = 13 * 60;

  const { setInitialState } = useModel('@@initialState');

  const timerRef = useRef<any>(null);
  const [desc, setDesc] = useState<string>();

  useEffect(() => {
    // console.log(' > effect');
    if (remainingSeconds <= 0) {
      setDesc('Your session is expired. Please reload page to login.');
      setInitialState((s) => ({ ...s, currentUser: undefined }));
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      loginOut();
    } else if (remainingSeconds > 0 && remainingSeconds < 30 * 60) {
      if (!timerRef.current) {
        // console.log(' > effect timer start');
        timerRef.current = setInterval(() => {
          // console.log('> effect...');
          const x = localStorage.getItem(LS_TOKEN_NAME)
            ? JSON.parse(atob((localStorage.getItem(LS_TOKEN_NAME) || '').split('.')[1]))
            : null;
          if (x) {
            const remainingMs = sn(x.exp) * 1000 - Date.now();
            setDesc(`Your session will be expired in ${dayjs(remainingMs).format('m:ss')} seconds`);
          }
        }, 1000);
      }
    } else {
      setDesc('');
    }
  }, [remainingSeconds, setInitialState]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        // console.log(' > effect timer end');
        clearInterval(timerRef.current);
      }
    };
  }, []);

  /* useEffect(() => {
    console.log('effect...');
    const timer = setInterval(() => {
      const x = JSON.parse(atob((localStorage.getItem(LS_TOKEN_NAME) || '').split('.')[1]));
      const expTime2 = sn(x?.exp) * 1000;

      console.log('timer...');
      if (expTime2 <= Date.now()) {
        setDesc('Your session is expired. Please reload page to login.');
      } else if (startTimer) {
        // setDesc(`Your session will be expired in ${dayjs(expTime2).fromNow(true)}. ${Date.now()}`);
        setDesc(`Your session will be expired in ${dayjs(expTime2).format('m:ss')} seconds`);
      } else {
        setDesc('');
      }
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [startTimer]); */

  return (
    <>
      {!!tmp?.exp && !!desc && (
        /* (remainingSeconds) < 600 && */ <div style={{ position: 'fixed', right: 24, top: 60 }}>
          <Alert
            closable
            type="warning"
            style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
            description={
              <>
                {desc}
                {/* ({Util.dtToDMY(sn(tmp?.exp) * 1000, `${DT_FORMAT_DMY} HH:mm`)}) */}
              </>
            }
          />
        </div>
      )}
    </>
  );
};
export default SessionTimeout;
