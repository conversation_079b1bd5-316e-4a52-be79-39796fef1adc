import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import useUserOptions from '@/hooks/BasicData/useUserOptions';
import CreateEmailForm from '@/pages/Email/EmailList/components/CreateEmailForm';
import { getEmailTemplateCatACList } from '@/services/app/Email/email-template-category';
import { getSupplier2EmailMatrix } from '@/services/app/Supplier/supplier';
import { getSupplierGroupACList } from '@/services/app/Supplier/supplier-group';
import Util, { nl2br, sEllipsed, sLeftPad, sn } from '@/util';
import { InfoCircleOutlined, MailOutlined, SendOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { But<PERSON>, Card, Col, message, Popover, Row, Space, Tag, Typography } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import EmailTrackingListModal from './components/EmailTrackingListModal';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import { dsEmailTrackingData } from '@/services/app/Sys/sys-log';

export type SearchFormValueType = Partial<API.Supplier>;
export type OfferSelectionFormValueType = { offer_id?: number };

const SupplierEmailMatrix: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Supplier>();
  const [templates, setTemplates] = useState<API.EmailTemplate[]>([]); // Email templates list
  const [columns, setColumns] = useState<ProColumns<API.Supplier>[]>([]); // Email templates list

  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false); // to Supplier

  // tracking modal
  const [lastEmailRow, setLastEmailRow] = useState<any>();
  const [openTrackingListModal, setOpenTrackingListModal] = useState<boolean>(false);

  // selection for batch actions
  const [selectedRowsState, setSelectedRows] = useState<API.Supplier[]>([]);
  const [openBulkSendMailModal, setOpenBulkSendMailModal] = useState<boolean>(false); // bulk Email
  const [receiver, setReceiver] = useState<string>('');

  const { userOptions } = useUserOptions();

  const orgColumns: ProColumns<API.Supplier>[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        sorter: true,
        hideInSearch: true,
        align: 'center',
        width: 130,
      },
      /* {
        title: 'Email',
        dataIndex: 'email',
        width: 170,
        showSorterTooltip: false,
        copyable: false,
      }, */
      {
        title: 'Name',
        dataIndex: 'firstname',
        width: 170,
        showSorterTooltip: false,
        render(__, entity) {
          return (
            <Row wrap={false} gutter={4}>
              <Col flex="auto">
                <Typography.Link
                  href={`${PUBLIC_PATH}suppliers/detail/${entity.id}`}
                  target="_blank"
                >
                  {entity.name}
                </Typography.Link>
              </Col>
              <Col flex="20px">
                {entity.ext?.note || entity.meta?.length ? (
                  <Popover
                    title={`${entity.name}`}
                    placement="right"
                    content={
                      <div
                        style={{ maxHeight: 250, overflowY: 'auto' }}
                        dangerouslySetInnerHTML={{ __html: nl2br(entity.ext?.note || '') }}
                      ></div>
                    }
                    styles={{ root: { maxWidth: 600 } }}
                  >
                    <InfoCircleOutlined />
                  </Popover>
                ) : null}
              </Col>
              <Col flex="20px">
                <Button
                  type="link"
                  size="small"
                  icon={<SendOutlined />}
                  title="Send Email to Supplier..."
                  onClick={() => {
                    setCurrentRow(entity);
                    handleReplyModalVisible(true);
                  }}
                />
              </Col>
            </Row>
          );
        },
      },
      {
        title: 'Trademark / Categories',
        dataIndex: 'meta',
        width: 200,
        showSorterTooltip: false,
        render(__, entity) {
          return entity.meta ? (
            <Space wrap={true} direction="vertical">
              <div>
                {entity.meta
                  .filter((x) => x.type == 'product_trademark')
                  .map((x) => (
                    <Tag key={x.value} color="purple">
                      {x.value}
                    </Tag>
                  ))}
              </div>
              <div>
                {entity.meta
                  .filter((x) => x.type == 'product_category')
                  .map((x) => (
                    <Tag key={x.value} color="green">
                      {x.label ?? x.value}
                    </Tag>
                  ))}
              </div>
            </Space>
          ) : null;
        },
      },
    ],
    [],
  );

  useEffect(() => {
    searchFormRef.current?.setFieldsValue(Util.getSfValues('cu_sf_supplier'));
  }, []);

  useEffect(() => {
    const newColumns = [...orgColumns];

    for (const x of templates) {
      const tmpCol: ProColumns<API.Supplier> = {
        title: (
          <div
            className="rotate-rl"
            style={{ paddingRight: 40, textAlign: 'center' }}
          >{`${sLeftPad(x.sort, 3, '0', '')} | ${sEllipsed(x.title, 20)}`}</div>
        ),
        dataIndex: x.id,
        width: 110,
        align: 'left',
        render(__, entity) {
          const matrix2 = entity.matrix2?.[`${x.id}`] || {};
          let cls = '';
          if (matrix2?.last_seen_on) {
            if (matrix2?.last_seen_on == matrix2?.sent_on) {
              cls += ' c-lightred';
            }
          }

          return (
            <Space direction="vertical" style={{ maxWidth: 95 }}>
              {/* <div>{Util.dtToDMYHHMM(entity.matrix?.[`${x.id}`]?.last_seen_on)}</div> */}
              <Popover
                trigger={['click']}
                className="cursor-pointer"
                content={<div>Sent on {Util.dtToDMYHHMM(matrix2?.sent_on)}</div>}
              >
                <div>
                  <span className={cls}>{Util.dtToDMYHHMM(matrix2?.last_seen_on)}</span>
                </div>
              </Popover>

              <div>
                {matrix2?.subject ? (
                  <Typography.Text
                    ellipsis
                    title={`${matrix2.id} | ${matrix2?.subject}`}
                    className="cursor-pointer"
                    onClick={() => {
                      setLastEmailRow(matrix2);
                      setOpenTrackingListModal(true);
                    }}
                  >
                    {matrix2?.subject}
                  </Typography.Text>
                ) : null}
              </div>
            </Space>
          );
        },
      };

      newColumns.push(tmpCol);
    }
    newColumns.push({ valueType: 'option' });

    setColumns(newColumns);
  }, [templates, orgColumns]);

  return (
    <PageContainer
      extra={
        <Space size={24}>
          <Button
            type="primary"
            ghost
            className="btn-green"
            onClick={() => {
              const hide = message.loading('Down Syncing email tracking info...', 0);
              dsEmailTrackingData()
                .then(() => {
                  message.success('Synced successfully!');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Sync Email Tracking Data
          </Button>
        </Space>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: 'auto' }}>{dom}</div>;
            },
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit', style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name="category_id"
            label="Category"
            request={(params) => {
              return getEmailTemplateCatACList(params);
            }}
            width="sm"
            fieldProps={{
              popupMatchSelectWidth: false,
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
            required
            allowClear
            showSearch
          />

          <ProFormSelect
            name="user_id"
            label="WHC User"
            options={userOptions}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            width={120}
            showSearch
            allowClear
          />

          <ProFormText
            name={'keyWords'}
            label="KeyWords"
            width={300}
            placeholder={'Search by Email / First Name / Name / Company'}
          />
        </ProForm>
      </Card>

      <ProTable<API.Supplier, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Supplier X Email Matrix</span>
          </Space>
        }
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(
            Util.getSfValues('cu_sf_supplier_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('cu_sf_supplier', searchFormValues);
          Util.setSfValues('cu_sf_supplier_p', params);

          // todo
          /* if (!searchFormValues.category_id) {
            return Promise.resolve([]);
          } */

          setLoading(true);
          return getSupplier2EmailMatrix(
            {
              ...params,
              with: 'address,address.country,ext,ext.user,meta,contacts',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }

              setTemplates(res.templates || []);

              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => x.id as React.Key),
          onChange(selectedRowKeys, selectedRows, info) {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
        columnEmptyText=""
      />

      <CreateEmailForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={{
          receiver: currentRow?.contacts
            ? Util.emailBuildSender(
                currentRow?.contacts?.[0]?.email,
                currentRow?.contacts?.[0]?.fullname,
              )
            : '',
        }}
        supplier={{
          id: currentRow?.id,
          name: currentRow?.name,
          address: currentRow?.address,
          contacts: currentRow?.contacts,
          created_on: currentRow?.created_on,
        }}
        onSubmit={async (value) => {
          setCurrentRow(undefined);
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      {lastEmailRow?.id ? (
        <EmailTrackingListModal
          emailId={lastEmailRow.id}
          emailAddr={lastEmailRow.email}
          modalVisible={openTrackingListModal}
          handleModalVisible={setOpenTrackingListModal}
        />
      ) : null}

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'Supplier'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <Button
            type="primary"
            icon={<MailOutlined />}
            onClick={() => {
              if (selectedRowsState.length) {
                const str = selectedRowsState
                  .filter((x) => !!x.contacts)
                  .map((x) => `${x.contacts?.[0]?.fullname} <${x.contacts?.[0]?.email}>`)
                  .join(',');
                setReceiver(str);
                setOpenBulkSendMailModal(true);
              }
            }}
          >
            Send Mails
          </Button>
        </FooterToolbar>
      )}

      <CreateEmailForm
        modalVisible={openBulkSendMailModal}
        handleModalVisible={setOpenBulkSendMailModal}
        initialValues={{ receiver: receiver }}
        htmlEditorId="bulk_email"
        onSubmit={async (FormData) => {
          setSelectedRows([]);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default SupplierEmailMatrix;
