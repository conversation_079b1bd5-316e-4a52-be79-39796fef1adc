import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Modal } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateSupplierCall } from '@/services/app/Supplier/supplier-call';
import Util, { getFormData, sn } from '@/util';
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
  ProFormUploadDragger,
} from '@ant-design/pro-components';
import { LS_TOKEN_NAME, SupplierCallDirectionKv, SupplierCallType } from '@/constants';
import { SupplierCallTypeOptions } from './CreateForm';
import { useModel } from '@umijs/max';
import HtmlEditor from '@/components/HtmlEditor';
import { getTrademarkACList } from '@/services/app/Sys/trademark';
import { SProFormSelectProduct } from './SProFormSelectProduct';
import { RcFile, UploadFile } from 'antd/es/upload';
import { deleteFile } from '@/services/app/File/file';
import { DefaultOptionType } from 'antd/es/select';

type FormValueType = Omit<Partial<API.SupplierCall>, 'files'> & {
  products?: DefaultOptionType[];
  trademarks?: DefaultOptionType[];
} & { files?: UploadFile[] };

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  const formData = getFormData(fields);

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      if (file?.originFileObj) {
        formData.append(`files[${ind}]`, file?.originFileObj ?? '');
      }
    });
  }

  try {
    await updateSupplierCall(sn(fields.id), formData as any);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.SupplierCall>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SupplierCall) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;
  const { initialState } = useModel('@@initialState');

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible && formRef.current) {
      formRef.current.resetFields();
      const newValues = {
        ...(initialValues || {}),
        offer_id: initialValues?.offer_id ? +initialValues?.offer_id : null,
        copy_to_sk: initialState?.currentUser?.initials == 'SK' ? '' : 'yes',
        files: initialValues?.files?.map((x) => ({ ...x, url: `${API_URL}/api/${x.url}` })),
      };

      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, initialValues, initialState?.currentUser?.initials]);

  return (
    <ModalForm
      title={'Update Supplier Conversion'}
      width="800px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '90px' }}
      wrapperCol={{ flex: 'auto' }}
      initialValues={initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: initialValues?.id });
        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProFormRadio.Group name="type" label="Type" options={SupplierCallTypeOptions} />
      <ProFormDependency name={['type']}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer) return null;
          else
            return (
              <ProFormRadio.Group
                name="direction"
                label="Direction"
                valueEnum={SupplierCallDirectionKv}
              />
            );
        }}
      </ProFormDependency>

      {/* <ProFormDependency name={['type']}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer)
            return <ProFormDigit name="offer_no" label="Offer No" width="sm" />;
          else return null;
        }}
      </ProFormDependency> */}

      <ProFormDependency name={['type']}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer)
            return <ProFormTextArea name="comment" label="Comment" fieldProps={{ rows: 5 }} />;
          else return null;
        }}
      </ProFormDependency>

      <ProForm.Item
        name={'note'}
        label={'Notes'}
        style={{ width: '100%' }}
        labelCol={undefined}
        wrapperCol={{ span: 24 }}
      >
        <HtmlEditor
          id={`call_note_update`}
          initialFocus
          enableTextModule
          hideMenuBar
          toolbarMode={2}
          height={400}
        />
      </ProForm.Item>

      <ProFormUploadDragger
        name="files"
        label="Files"
        title={false}
        description="Please select files or drag & drop"
        // accept="image/*"
        fieldProps={{
          multiple: true,
          listType: 'picture-card',
          name: 'file',
          headers: {
            Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
          },
          style: { marginBottom: 24 },
          beforeUpload: (file: RcFile, fileList: RcFile[]) => {
            return false;
          },
          onRemove: async (file: API.File) => {
            if (file.id) {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to delete?',
                  onOk: async () => {
                    resolve(true);
                    const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);
                    const res = await deleteFile(sn(file.id));
                    hide();
                    if (res) {
                      message.success(`Deleted successfully!`);
                    } else {
                      Util.error(`Delete failed, please try again!`);
                    }

                    return res;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            } else {
              return true;
            }
          },
        }}
      />

      <ProFormRadio.Group
        name="copy_to_sk"
        label="Copy to SK"
        options={[
          { value: '', label: 'No' },
          { value: 'yes', label: 'Yes' },
          { value: 'yes2', label: 'Yes with files' },
        ]}
      />
    </ModalForm>
  );
};

export default UpdateForm;
