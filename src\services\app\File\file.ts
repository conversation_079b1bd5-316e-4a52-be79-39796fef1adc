/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/file';

/** 
 * Get all files list
 * 
 * GET /api/file */
export async function getFileList(
    params: Partial<API.PageParams & API.File>,
    sort?: any,
    filter?: any,
) {
    return request<API.Result<API.File>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}

/** 
 * delete file
 * 
 * DELETE /api/file/{id} */
export async function deleteFile(id: number) {
    return request<API.ResultObject<boolean>>(`${urlPrefix}/${id}`, {
        method: 'DELETE',
        paramsSerializer,
    }).then(res => res.message);
}