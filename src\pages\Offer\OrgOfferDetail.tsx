import Util from "@/util";
import { PageContainer, ProForm, ProFormInstance, ProFormSwitch } from "@ant-design/pro-components";
import { useModel, useParams } from "@umijs/max";
import { But<PERSON>, Card, Col, message, Row, Space, Spin, Splitter } from "antd";
import { useCallback, useEffect, useRef, useState } from "react";

import { useTheme } from "antd-style";
import { getOrgOffer } from "@/services/app/Offer/org-offer";
import OfferTemplateLangFormPartial from "../OfferTemplate/components/OfferTemplateLangFormPartial";
import { updateOfferTemplateBulk } from "@/services/app/Offer/offer-template";
import { DictCode } from "@/constants";
import OfferTemplateLangViewPartial from "../OfferTemplate/components/OfferTemplateLangViewPartial";
import ConversationsMixedPanel from "./components/ConversationsMixedPanel";

/**
 * Offer details for an offer from WHC_Org
 *
 *
 * @param props
 * @returns
 */
const OrgOfferDetail: React.FC = (props) => {
  const params = useParams();
  const { getDictByCode } = useModel("app-settings");

  const { offer_no, supplierId } = params || {};

  const theme = useTheme();

  const formRefTop = useRef<ProFormInstance>();

  const [loading, setLoading] = useState(false);
  const [supplier, setSupplier] = useState<API.Supplier>();
  const [orgOffer, setOrgOffer] = useState<APIOrg.Offer>();

  const [viewMode, setViewMode] = useState(0);
  const [rightPanelSize, setRightPanelSize] = useState(33); // percentage
  const [formData, setFormData] = useState();

  /**
   * Load Org Offer and pre-set in form.
   */
  const loadOrgOfferDetail = useCallback(() => {
    if (offer_no) {
      setLoading(true);
      getOrgOffer(offer_no, {
        with: "offerTemplate",
      })
        .then((res) => {
          setOrgOffer(res);
          const template = res.offer_template;

          formRefTop.current?.resetFields();
          if (template) {
            const tmp = template.offer_template_langs?.reduce((prev: any, langData) => {
              prev[`${langData.lang}`] = langData;
              return prev;
            }, {});
            tmp.files = template.files?.map((x) => ({ ...x, url: `${API_URL}/api/${x.url}` }));

            formRefTop.current?.setFieldsValue(tmp);
          } else {
            const tmp = {
              DE: {
                header: getDictByCode(DictCode.OFFER_SALUTATION_DE),
                footer: getDictByCode(DictCode.OFFER_FOOTER_DE),
              },
              EN: {
                header: getDictByCode(DictCode.OFFER_SALUTATION_EN),
                footer: getDictByCode(DictCode.OFFER_FOOTER_EN),
              },
            };
            formRefTop.current?.setFieldsValue(tmp);
          }
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    } else {
      setOrgOffer(undefined);
      formRefTop.current?.resetFields();
    }
  }, [getDictByCode, offer_no]);

  useEffect(() => {
    loadOrgOfferDetail();
  }, [loadOrgOfferDetail]);

  const handleSave = async () => {
    const hide = message.loading("Saving...", 0);

    try {
      const values = formRefTop.current?.getFieldsValue();
      const formData = new FormData();
      // Process each language section (DE, EN, etc.)
      Object.entries(values).forEach(([lang, langData]: [string, any]) => {
        if (lang == "EN" || lang == "DE") {
          // Add basic text fields
          if (langData.subject) {
            formData.append(`data[${lang}][subject]`, langData.subject);
          }
          if (langData.header) {
            formData.append(`data[${lang}][header]`, langData.header);
          }
          if (langData.body) {
            formData.append(`data[${lang}][body]`, langData.body);
          }
          if (langData.footer) {
            formData.append(`data[${lang}][footer]`, langData.footer);
          }

          formData.append(`data[${lang}][lang]`, lang);
        }
      });

      console.log("Step 1");

      // Handle file uploads
      if (values.files?.length) {
        values.files.forEach((file: any, index: number) => {
          // Only append file if it has originFileObj (new file)
          if (file?.originFileObj) {
            formData.append(`data[files][${index}]`, file.originFileObj);
          } else if (file.id) {
            // For existing files, just pass the ID
            formData.append(`data[existing_files][${index}]`, file.id);
          }
        });
      }
      if (offer_no) {
        formData.append("offer_no", offer_no);
      }

      // Call API to update offer templates
      const result = await updateOfferTemplateBulk(formData);

      hide();
      message.success("Templates saved successfully");

      loadOrgOfferDetail();

      return result;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  useEffect(() => {
    if (viewMode == 1) {
      setRightPanelSize(66);
    } else setRightPanelSize(33);
  }, [viewMode]);

  return (
    <PageContainer title={false}>
      <ProForm layout="vertical" formRef={formRefTop} isKeyPressSubmit className="search-form" submitter={false}>
        <Spin spinning={loading} wrapperClassName="w-full">
          <Card variant="borderless" style={{ marginBottom: 8, minHeight: 100 }}>
            &nbsp;
          </Card>
        </Spin>

        <Spin spinning={loading} wrapperClassName="w-full">
          <Splitter style={{ height: 600, boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)", background: "#fff" }}>
            <Splitter.Panel>
              <Splitter layout="vertical">
                <Splitter.Panel>
                  <div style={{ padding: 16 }}>
                    {viewMode === 0 ? (
                      <OfferTemplateLangFormPartial
                        renderSaveButton={() => {
                          return (
                            <Button type="primary" onClick={handleSave}>
                              {orgOffer?.offer_template?.id ? "Save" : "Create"}
                            </Button>
                          );
                        }}
                      />
                    ) : (
                      <OfferTemplateLangViewPartial values={formData} />
                    )}
                  </div>
                </Splitter.Panel>
                <Splitter.Panel defaultSize="10%">
                  <h1>Bottom</h1>
                </Splitter.Panel>
              </Splitter>
            </Splitter.Panel>
            <Splitter.Panel collapsible resizable size={`${rightPanelSize}%`} max={`${rightPanelSize}%`}>
              <div style={{ padding: 16 }}>
                <Row justify="space-between" wrap={false}>
                  <Col flex="auto">
                    <h3>Offer No: {offer_no}</h3>
                  </Col>
                  <Col>
                    <ProFormSwitch
                      name="viewMode"
                      fieldProps={{
                        checked: viewMode == 1,
                        checkedChildren: "View",
                        unCheckedChildren: "Edit",
                        onChange(value) {
                          if (value) {
                            setFormData(formRefTop.current?.getFieldsValue());
                          }
                          setViewMode(value ? 1 : 0);
                        },
                      }}
                      formItemProps={{ style: { marginBottom: 0 } }}
                    />
                  </Col>
                </Row>
                <ConversationsMixedPanel />
              </div>
            </Splitter.Panel>
          </Splitter>
        </Spin>
      </ProForm>
    </PageContainer>
  );
};

export default OrgOfferDetail;
