/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/file-log';

/** rule GET /api/sys/file-log */
export async function getFileLog(params?: API.PageParams, sort?: any, filter?: any): Promise<any> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
